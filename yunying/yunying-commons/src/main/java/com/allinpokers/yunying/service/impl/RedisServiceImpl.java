package com.allinpokers.yunying.service.impl;

import com.allinpokers.yunying.model.job.ClubChangeTribeJob;
import com.allinpokers.yunying.model.job.TribeDelClubJob;
import com.allinpokers.yunying.service.RedisService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.allinpokers.yunying.enu.redis.RedisCacheKeyEnum.*;

@Service
@Slf4j
public class RedisServiceImpl implements RedisService {

    @Resource
    private RedisTemplate redisTemplate;
    private static final String KEY_ROOM_ROOMSETTLEMENT_PREFIX="room:roomSettlement:";
    private static final String KEY_ROOM_PREFIX="room:roomListState:";
    @Override
    public Set<String> getClubBringRooms(String clubId) {
        return redisTemplate.opsForSet().members(CLUB_BRING_ROOMS.getKey() + clubId);
    }
    /**
     * 获取玩家提前离桌过的牌局
     * @return
     */
    @Override
    public Set<String> getUserLeaveTableRooms() {
        return redisTemplate.opsForSet().members(KEY_ROOM_ROOMSETTLEMENT_PREFIX+"*");
    }

    @Override
    public Set<Integer> getLeaveTableRooms(int userId) {
        return redisTemplate.opsForSet().members(KEY_ROOM_ROOMSETTLEMENT_PREFIX+userId);
    }

    @Override
    public Map<Integer, String> getRooms() {
        Map<Integer,String> getRoom=new HashMap<>();
        try {
            Set<String> members = redisTemplate.keys(KEY_ROOM_PREFIX + "*");
            for (String member : members) {
                String[] split = member.split(":");
                RedisSerializer valueSerializer = redisTemplate.getValueSerializer();
                redisTemplate.setValueSerializer(new StringRedisSerializer());
                String s1 = String.valueOf(redisTemplate.opsForValue().get(KEY_ROOM_PREFIX + split[2]));
                redisTemplate.setValueSerializer(valueSerializer);
                getRoom.put(Integer.parseInt(split[2]),s1);
            }
        }catch (Exception e){
            log.error(e.getMessage());
        }
        return getRoom;
    }


    /**
     * 转移任务
     *
     * @param clubId
     * @param job
     */
    @Override
    public void addClubChangeTribeJob(String clubId, ClubChangeTribeJob job) {
        redisTemplate.opsForHash().put(CLUB_CHANGE_TRIBE_JOB.getKey(), clubId, job);
    }

    @Override
    public Set<String> getClubChangeTribeJob() {
        return redisTemplate.opsForHash().keys(CLUB_CHANGE_TRIBE_JOB.getKey());
    }

    @Override
    public ClubChangeTribeJob getClubChangeTribeJob(String clubId) {
        return (ClubChangeTribeJob) redisTemplate.opsForHash().get(CLUB_CHANGE_TRIBE_JOB.getKey(), clubId);
    }

    @Override
    public void deleteClubChangeTribeJob(String clubId) {
        redisTemplate.opsForHash().delete(CLUB_CHANGE_TRIBE_JOB.getKey(), clubId);
    }

    /**
     * 踢出俱乐部任务
     *
     * @param clubId 俱乐部id
     * @param job    任务
     */
    @Override
    public void addClubTribeDelJob(String clubId, TribeDelClubJob job) {
        redisTemplate.opsForHash().put(CLUB_TRIBE_DEL_JOB.getKey(), clubId, job);
    }


    /**
     * 获取踢出俱乐部任务
     *
     * @param clubId
     * @return
     */
    @Override
    public TribeDelClubJob getClubTribeDelJob(String clubId) {
        return (TribeDelClubJob) redisTemplate.opsForHash().get(CLUB_TRIBE_DEL_JOB.getKey(), clubId);
    }

    /**
     * 踢出俱乐部任务列表key
     *
     * @return
     */
    @Override
    public Set<String> getClubTribeDelJobList() {
        return redisTemplate.opsForHash().keys(CLUB_TRIBE_DEL_JOB.getKey());
    }

    /**
     * 删除俱乐部踢出任务
     *
     * @param clubId
     */
    @Override
    public void deleteClubTribeDelJob(String clubId) {
        redisTemplate.opsForHash().delete(CLUB_TRIBE_DEL_JOB.getKey(), clubId);
    }

    /**
     * 添加到取消房间队列
     */
    @Override
    public void addCancelledRoom(String cancelledSetKey, Integer roomId, int closeType) {
        String member = closeType + "," + roomId;
        redisTemplate.opsForSet().add(cancelledSetKey, member);
    }
    private String SYSTEM_CALCULATEPROMOTIONBEANS_TESTING_REDIS_KEY = "system:calculator:calculatePromotionBeans";
    @Override
    public void addCalculatorRestatr(String value) {
        redisTemplate.opsForValue().set(SYSTEM_CALCULATEPROMOTIONBEANS_TESTING_REDIS_KEY, value);
    }

    @Override
    public String getCalculatorRestatr() {
        return redisTemplate.opsForValue().get(SYSTEM_CALCULATEPROMOTIONBEANS_TESTING_REDIS_KEY)+"";
    }
}
