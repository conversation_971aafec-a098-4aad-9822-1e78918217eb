package com.allinpokers.yunyingjob.oss.service;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSSException;
import org.springframework.web.multipart.MultipartFile;

public interface OssService {

    /**
     * 文件上传
     *
     * @param file  文件
     * @param operatorId
     * @param fromType 6导出任务上传
     */
    String uploadFile(MultipartFile file, Boolean fullPath, Integer operatorId, Integer fromType) throws OSSException, ClientException, IllegalStateException;

    /**
     * 删除资源
     *
     * @param path  资源地址
     */
    void deleteFile(String path);
}
