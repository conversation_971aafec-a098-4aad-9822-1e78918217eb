package com.allinpokers.yunyingjob.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 德州扑克用户基本信息表  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserBasicInfo {
    /**
     * 账号（主键）
     */
    @ApiModelProperty("账号（主键）")
    private Integer userId;

    /**
     * 密码（暂时保留不用）
     */
    @ApiModelProperty("密码（暂时保留不用）")
    private String password;

    /**
     * 唯一标示(游客用户没有)
     */
    @ApiModelProperty("唯一标示(游客用户没有)")
    private String mark;

    /**
     * （0-99）账号类型
     */
    @ApiModelProperty("（0-99）账号类型")
    private Integer type;

    /**
     * 0 未激活 1 已激活
     */
    @ApiModelProperty("0 未激活 1 已激活")
    private Integer active;

    /**
     * 软件下载渠道
     */
    @ApiModelProperty("软件下载渠道")
    private Integer channel;

    /**
     * 注册时间
     */
    @ApiModelProperty("注册时间")
    private LocalDateTime regTime;

    /**
     * 激活时间
     */
    @ApiModelProperty("激活时间")
    private LocalDateTime activeTime;

    /**
     * 用户IMSI
     */
    @ApiModelProperty("用户IMSI")
    private String imsi;

    /**
     * mac
     */
    @ApiModelProperty("mac")
    private String mac;

    /**
     * screen
     */
    @ApiModelProperty("screen")
    private String screen;

    /**
     * phoneModel
     */
    @ApiModelProperty("phoneModel")
    private String phoneModel;

    /**
     * systemVersion
     */
    @ApiModelProperty("systemVersion")
    private String systemVersion;

    /**
     * token
     */
    @ApiModelProperty("token")
    private String token;

    /**
     * 注册地区
     */
    @ApiModelProperty("注册地区")
    private String area;

    /**
     * 登录次数
     */
    @ApiModelProperty("登录次数")
    private Integer loginCount;

    /**
     * 0-冻结,1-正常 
     */
    @ApiModelProperty("0-冻结,1-正常 ")
    private Integer forbid;

    /**
     * 设备机器码
     */
    @ApiModelProperty("设备机器码")
    private String imei;
}