<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunyingjob.dao.crazypoker.MessageUnreadDao">
    <insert id="insertIgnore">
        insert ignore into message_unread
        (user_id,
         club_num, club_msg,
         tribe_num, tribe_msg,
         system_num, system_msg,
         bag_num, bag_msg,
         kdou_num, kdou_msg,
         create_time, update_time)
            value (#{userId},
                   0, '',
                   0, '',
                   0, '',
                   0, '',
                   0, '',
                   now(), now())
    </insert>

    <update id="addClubUnread">
        update message_unread
        set club_num = club_num + 1,
            club_msg = #{msg},
            update_time = now()
        where user_id = #{userId}
    </update>

    <update id="addTribeUnread">
        update message_unread
        set tribe_num = tribe_num + 1,
            tribe_msg = #{msg},
            update_time = now()
        where user_id = #{userId}
    </update>
</mapper>