package com.allinpokers.yunying.model.response.gamedetail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 联盟玩家牌局信息
 *
 * <AUTHOR>
 */
@ApiModel("联盟玩家牌局信息")
@Data
public class TribeUserStat {
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private Integer userId;
    @ApiModelProperty("用户昵称")
    private String nickname;
    @ApiModelProperty("用户随机ID")
    private String randomNum;
    @ApiModelProperty("俱乐部名称")
    private String clubName;
    @ApiModelProperty("总积分")
    private Integer totalIntegral = 0;
    @ApiModelProperty("总保险")
    private Integer totalInsurance = 0;
    @ApiModelProperty("总手数")
    private Integer totalHands = 0;
    @ApiModelProperty("总房间数")
    private Integer totalRoom = 0;
    @ApiModelProperty("总带入次数")
    private Integer totalBringTimes = 0;
    @ApiModelProperty("总JP")
    private Integer totalJackpot = 0;
    @ApiModelProperty("总JP Real")
    private Integer totalJackpotReal = 0;
    @ApiModelProperty("总JP利润")
    private Integer totalJackpotProfit = 0;
    @ApiModelProperty("GPS")
    private String gps = "";
    @ApiModelProperty("常用手机设备")
    private String commonPhone = "";
    @ApiModelProperty("最近带入地点")
    private String recentBringAddr = "";
    @ApiModelProperty("常用IP")
    private String commonIp = "";
    @ApiModelProperty(value = "最后一次的俱乐部ID", hidden = true)
    @JsonIgnore
    private Integer clubId;

    public TribeUserStat incTotalIntegral(int integral) {
        this.totalIntegral += integral;
        return this;
    }

    public TribeUserStat incTotalBrings(int brings) {
        this.totalBringTimes += brings;
        return this;
    }

    public TribeUserStat incTotalInsurance(int insurance) {
        this.totalInsurance += insurance;
        return this;
    }

    public TribeUserStat incTotalHands(int hands) {
        this.totalHands += hands;
        return this;
    }

    public TribeUserStat incTotalRoom(int totalRoom) {
        this.totalRoom += totalRoom;
        return this;
    }


    public TribeUserStat incTotalJackpot(int jackpot) {
        this.totalJackpot += jackpot;
        return this;
    }

    public TribeUserStat incTotalJackpotReal(int jackpotReal) {
        this.totalJackpotReal += jackpotReal;
        return this;
    }

    public TribeUserStat incTotalJackProfit(int jackpotProfit) {
        this.totalJackpotProfit += jackpotProfit;
        return this;
    }

}
