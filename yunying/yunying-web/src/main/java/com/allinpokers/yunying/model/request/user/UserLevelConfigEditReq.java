package com.allinpokers.yunying.model.request.user;

import com.allinpokers.yunying.permission.security.UserInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UserLevelConfigEditReq {

    @ApiModelProperty("用户ID")
    private Integer id;
    @ApiModelProperty("用户层次代号")
    private Integer levelCode;
    @ApiModelProperty("显示名称")
    private String levelShowName;
    /**
     * 操作的用户
     */
    @ApiModelProperty(hidden = true)
    @JsonIgnore
    private UserInfo operatorUser;
}
