package com.allinpokers.yunying.dao.vest;

import com.allinpokers.yunying.dao.BaseDao;
import com.allinpokers.yunying.entity.plus.vest.IosVestPromoteChannelTimes;
import com.allinpokers.yunying.entity.vest.IosVestPromote;
import com.allinpokers.yunying.entity.vest.example.IosVestPromoteExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * IosVestPromoteDao  Mapper
 * 
 * <AUTHOR>
 */
@Mapper
public interface IosVestPromoteDao extends BaseDao<IosVestPromote, IosVestPromoteExample, Integer> {


    @Select("select type,ios_channel_id as iosChannelId,count(1) times from ios_vest_promote group by type,ios_channel_id")
    List<IosVestPromoteChannelTimes> selectListGroupByChannel();
}