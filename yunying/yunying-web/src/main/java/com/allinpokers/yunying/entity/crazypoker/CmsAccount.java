package com.allinpokers.yunying.entity.crazypoker;

import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * CmsAccount  Entity
 * 
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CmsAccount {
    /**
     * cms用户id
     */
    @ApiModelProperty("cms用户id")
    private Integer cmsUid;

    /**
     * 金豆数
     */
    @ApiModelProperty("金豆数")
    private Long chip;

    /**
     * 锁定金豆(代充渠道充值预扣时增加,充值失败或者成功返还减少)
     */
    @ApiModelProperty("锁定金豆(代充渠道充值预扣时增加,充值失败或者成功返还减少)")
    private Long lockChip;

    /**
     * createdTime
     */
    @ApiModelProperty("createdTime")
    private LocalDateTime createdTime;

    /**
     * updatedTime
     */
    @ApiModelProperty("updatedTime")
    private LocalDateTime updatedTime;
}