package com.allinpokers.yunying.model.response.gamedetail;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 平台牌局信息
 *
 * <AUTHOR>
 */
@ApiModel("平台牌局信息")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PlatformRoomStat {
    @ApiModelProperty(value = "房间ID")
    private Integer roomId;

    @ApiModelProperty(value = "房间名")
    private String roomName;

    @ApiModelProperty("房间类型")
    private Integer clubRoomType;

    @ApiModelProperty("是否开启保险，1是，0否")
    private Integer insurance = 0;

    @ApiModelProperty("保险池")
    private Integer insurancePool = 0;

    @ApiModelProperty(value = "用户ID", hidden = true)
    private Integer userId;

    @ApiModelProperty("玩家昵称")
    private String userNickname;

    @ApiModelProperty("用户随机ID")
    private String userRandomId;

    @ApiModelProperty(value = "联盟ID", hidden = true)
    private Integer tribeId;

    @ApiModelProperty("联盟名称")
    private String tribeName;

    @ApiModelProperty(value = "俱乐部ID", hidden = true)
    private Integer clubId;

    @ApiModelProperty("俱乐部名称")
    private String clubName;

    @ApiModelProperty("带入")
    private Integer bring;

    @ApiModelProperty("盲注级别")
    private String blind;

    @ApiModelProperty("手数")
    private Integer hand;

    @ApiModelProperty("结束时间")
    private LocalDateTime endTime;
}
