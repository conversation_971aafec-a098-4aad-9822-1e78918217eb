package com.allinpokers.yunying.useronline.bean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

@Builder
@Data
public class LinkStream {
    /**
     * id
     * */
    private Integer id;
    /**
     * 用户ID
     * */
    private Integer userId;
    /**
     * 用户昵称
     * */
    private String nike_name;
    /**
     * 下注金额
     * */
    private Integer chip;
    /**
     * 俱乐部ID
     * */
    private Integer club_id;
    /**
     * 游戏类型
     * */
    private Integer gameType;
    /**
     * 结算时间
     * */
    private Date recordTime;
    /**
     * 类型 1 全参与  2 俱乐部 3  联盟
     * */
    private Integer labeling;
    /**
     * 输赢
     * */
    private Integer win_chip;
    /**
     * 记录类型
     * */
    private Integer stream_type;
    /**
     * 房间ID
     * */
    private Integer room_id;
    /**
     * 桌子ID
     * */
    private Integer table_id;
    /**
     * 俱乐部输赢
     * */
    private Integer clubWin;
    /**
     * 联盟输赢
     * */
    private Integer rebateWin;
    /**
     * 抽水金额
     * */
    private Integer taxChip;
    /**
     * 中彩金额
     * */
    private Integer jackChip;

    private int clubRoomType;
}
