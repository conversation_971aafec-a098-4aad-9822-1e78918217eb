package com.allinpokers.yunying.activity.api.req;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDate;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class StatisticsByDateReq {

    @ApiModelProperty(value = "开始日期, yyyy-MM-dd", example = "2019-06-01")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate startDate;

    @ApiModelProperty("结束日期, yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate endDate;
}
