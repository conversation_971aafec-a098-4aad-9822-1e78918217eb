package com.allinpokers.yunying.broadcast.service;


import com.allinpokers.yunying.broadcast.bean.SysBroadcast;
import com.allinpokers.yunying.broadcast.bean.SysBroadcastQuery;
import com.allinpokers.yunying.broadcast.dao.SysBroadcastDao;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * SysBroadcastService
 *
 * <AUTHOR>
 * @date 2025/2/12
 */
@Slf4j
@Service
public class SysBroadcastService {

    @Resource
    SysBroadcastDao sysBroadcastDao;

    /**
     * 分页查询
     * @param query 查询条件
     * @return 分页数据
     */
    public PageInfo<SysBroadcast> findPage(SysBroadcastQuery query) {
        // 默认分页为1，每页20条
        if (query.getPageNum() == null) {
            query.setPageNum(1);
        }

        if (query.getPageSize() == null) {
            query.setPageSize(20);
        }
        PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<SysBroadcast> sysBroadcasts = sysBroadcastDao.findAll(query);
        return new PageInfo<>(sysBroadcasts);
    }

    /**
     * 新增
     * @param sysBroadcast 广播
     */
    public void add(SysBroadcast sysBroadcast) {
        sysBroadcastDao.add(sysBroadcast);
    }

    /**
     * 删除
     * @param id 广播id
     */
    public void remove(Long id) {
        sysBroadcastDao.remove(id);
    }

    /**
     * 查询
     * @param id 广播id
     * @return 广播
     */
    public SysBroadcast find(Long id) {
        return sysBroadcastDao.find(id);
    }

}
