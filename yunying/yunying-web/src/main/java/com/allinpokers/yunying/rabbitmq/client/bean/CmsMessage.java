package com.allinpokers.yunying.rabbitmq.client.bean;

import com.allinpokers.yunying.rabbitmq.client.bean.cms.CmsParams;
import com.allinpokers.yunying.rabbitmq.constant.ECmsMessageCode;
import lombok.*;

/**
 * CMS - 消息
 *
 * <AUTHOR>
 */
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class CmsMessage {
    /**
     * 发送者ID
     */
    private String senderId;
    /**
     * 消息类型
     *
     * @see ECmsMessageCode
     */
    private Integer type;
    /**
     * json参数
     *
     * @see CmsParams
     */
    private String params;
}
