<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allinpokers.yunying.assignment.dao.ATUserDao">
  <resultMap id="BaseResultMap" type="com.allinpokers.yunying.assignment.dao.model.ATUser">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="room_path" jdbcType="INTEGER" property="roomPath" />
    <result column="user_type" jdbcType="VARCHAR" property="userType" />
    <result column="visit_level" jdbcType="VARCHAR" property="visitLevel" />
    <result column="blind" jdbcType="VARCHAR" property="blind" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="start_time" jdbcType="TIME" property="startTime" />
    <result column="end_time" jdbcType="TIME" property="endTime" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="created_by" jdbcType="INTEGER" property="createdBy" />
    <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="updated_by" jdbcType="INTEGER" property="updatedBy" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    user_id, room_path, user_type, visit_level, blind, status, start_time, end_time, 
    created_time, created_by, updated_time, updated_by
  </sql>
  <select id="selectByExample" parameterType="com.allinpokers.yunying.assignment.dao.model.example.ATUserExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from at_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from at_user
    where user_id = #{userId,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from at_user
    where user_id = #{userId,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.allinpokers.yunying.assignment.dao.model.example.ATUserExample">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from at_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.allinpokers.yunying.assignment.dao.model.ATUser">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into at_user (user_id, room_path, user_type, 
      visit_level, blind, status, 
      start_time, end_time, created_time, 
      created_by, updated_time, updated_by
      )
    values (#{userId,jdbcType=INTEGER}, #{roomPath,jdbcType=INTEGER}, #{userType,jdbcType=VARCHAR}, 
      #{visitLevel,jdbcType=VARCHAR}, #{blind,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{startTime,jdbcType=TIME}, #{endTime,jdbcType=TIME}, #{createdTime,jdbcType=TIMESTAMP}, 
      #{createdBy,jdbcType=INTEGER}, #{updatedTime,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.allinpokers.yunying.assignment.dao.model.ATUser">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into at_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="roomPath != null">
        room_path,
      </if>
      <if test="userType != null">
        user_type,
      </if>
      <if test="visitLevel != null">
        visit_level,
      </if>
      <if test="blind != null">
        blind,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="startTime != null">
        start_time,
      </if>
      <if test="endTime != null">
        end_time,
      </if>
      <if test="createdTime != null">
        created_time,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedTime != null">
        updated_time,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="roomPath != null">
        #{roomPath,jdbcType=INTEGER},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=VARCHAR},
      </if>
      <if test="visitLevel != null">
        #{visitLevel,jdbcType=VARCHAR},
      </if>
      <if test="blind != null">
        #{blind,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        #{startTime,jdbcType=TIME},
      </if>
      <if test="endTime != null">
        #{endTime,jdbcType=TIME},
      </if>
      <if test="createdTime != null">
        #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=INTEGER},
      </if>
      <if test="updatedTime != null">
        #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.allinpokers.yunying.assignment.dao.model.example.ATUserExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from at_user
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update at_user
    <set>
      <if test="record.userId != null">
        user_id = #{record.userId,jdbcType=INTEGER},
      </if>
      <if test="record.roomPath != null">
        room_path = #{record.roomPath,jdbcType=INTEGER},
      </if>
      <if test="record.userType != null">
        user_type = #{record.userType,jdbcType=VARCHAR},
      </if>
      <if test="record.visitLevel != null">
        visit_level = #{record.visitLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.blind != null">
        blind = #{record.blind,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.startTime != null">
        start_time = #{record.startTime,jdbcType=TIME},
      </if>
      <if test="record.endTime != null">
        end_time = #{record.endTime,jdbcType=TIME},
      </if>
      <if test="record.createdTime != null">
        created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=INTEGER},
      </if>
      <if test="record.updatedTime != null">
        updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update at_user
    set user_id = #{record.userId,jdbcType=INTEGER},
      room_path = #{record.roomPath,jdbcType=INTEGER},
      user_type = #{record.userType,jdbcType=VARCHAR},
      visit_level = #{record.visitLevel,jdbcType=VARCHAR},
      blind = #{record.blind,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      start_time = #{record.startTime,jdbcType=TIME},
      end_time = #{record.endTime,jdbcType=TIME},
      created_time = #{record.createdTime,jdbcType=TIMESTAMP},
      created_by = #{record.createdBy,jdbcType=INTEGER},
      updated_time = #{record.updatedTime,jdbcType=TIMESTAMP},
      updated_by = #{record.updatedBy,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.allinpokers.yunying.assignment.dao.model.ATUser">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update at_user
    <set>
      <if test="roomPath != null">
        room_path = #{roomPath,jdbcType=INTEGER},
      </if>
      <if test="userType != null">
        user_type = #{userType,jdbcType=VARCHAR},
      </if>
      <if test="visitLevel != null">
        visit_level = #{visitLevel,jdbcType=VARCHAR},
      </if>
      <if test="blind != null">
        blind = #{blind,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="startTime != null">
        start_time = #{startTime,jdbcType=TIME},
      </if>
      <if test="endTime != null">
        end_time = #{endTime,jdbcType=TIME},
      </if>
      <if test="createdTime != null">
        created_time = #{createdTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=INTEGER},
      </if>
      <if test="updatedTime != null">
        updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=INTEGER},
      </if>
    </set>
    where user_id = #{userId,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.allinpokers.yunying.assignment.dao.model.ATUser">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update at_user
    set room_path = #{roomPath,jdbcType=INTEGER},
      user_type = #{userType,jdbcType=VARCHAR},
      visit_level = #{visitLevel,jdbcType=VARCHAR},
      blind = #{blind,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      start_time = #{startTime,jdbcType=TIME},
      end_time = #{endTime,jdbcType=TIME},
      created_time = #{createdTime,jdbcType=TIMESTAMP},
      created_by = #{createdBy,jdbcType=INTEGER},
      updated_time = #{updatedTime,jdbcType=TIMESTAMP},
      updated_by = #{updatedBy,jdbcType=INTEGER}
    where user_id = #{userId,jdbcType=INTEGER}
  </update>
</mapper>