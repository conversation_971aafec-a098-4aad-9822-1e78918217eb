package com.dzpk.processor.impl;

import com.dzpk.insurance.Holder;
import com.dzpk.common.utils.LogUtil;
import org.apache.logging.log4j.Logger;

import com.dzpk.processor.IProcessor;
import com.dzpk.work.Task;
import com.i366.cache.Cache;
import com.i366.model.room.Room;

/**
 * 保险操作超时
 * <AUTHOR>
 *
 */
public class Task_10011 implements IProcessor {

    private Logger logger = LogUtil.getLogger(Task_10011.class);
    
    @Override
    public void handle(Task task) {
        if (!task.isValid()) {
            logger.debug("invalid message");
            return;
        }
        
        logger.debug("Task_10011 timesout!!!!");
        Room room = Cache.getRoom(task.getRoomId(), task.getRoomPath());
        logger.debug("roomId: " + task.getRoomId() + ", roomPath: " + task.getRoomPath());
        // 保险操作超时
        if (room != null) {
            if ((Long) task.getMap().get(1) < room.getInsureIndex()) {
                logger.debug("invalid insure timeout task!");
                return;
            }

            Long taskBetIndex = (Long) task.getMap().get(2);
            logger.debug("room.getBetIndex=" + room.getBetIndex() + ", task.betIndex()=" + taskBetIndex);
            if (room.getBetIndex() == taskBetIndex) {
                // 同一手牌启动的任务超时，客户端未发401操作过来，需要自动走弃保逻辑
                for(Integer userId : room.getInsurer().getHolderMap().keySet()) {
                    Holder holder = room.getInsurer().getHolderByUserId(userId);
                    if (null != holder && 0 == holder.getStatus()) {
                        logger.debug("userId=" + userId + " need accept insurance auto");
                        room.getInsurer().acceptInsurance(room, userId);
                    }
                }
            } else {
                logger.debug("invalid insure timeout task!");
            }
            room.roomProcedure.status9();
            
            room.roomProcedure.delayTaskMap.remove(task.getId());
        }
    }

}
