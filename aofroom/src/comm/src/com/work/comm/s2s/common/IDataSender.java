package com.work.comm.s2s.common;

/**
 * 数据包发送器
 */
public interface IDataSender {
    /**
     * 发送数据包
     * 由上层业务调用
     *
     * @param type      对端APP类型,必填
     * @param sendAll   是否向所有服务器发送
     *                  true  : 是
     *                  false : 否
     * @param data      待发送的消息,必填
     *
     * @throws Exception
     *    IllegalArgumentException 参数无效
     */
    void sendDataBy(String type, boolean sendAll, byte[] data) throws Exception;

    /**
     * 发送数据包
     * 由上层业务调用
     *
     * @param type      对端APP类型,必填
     * @param serverId  对端App的标识符，类型内唯一，可选
     *                  当不指定时，则是向此类型的消息发送
     * @param sendAll   是否向所有服务器发送
     *                  true  : 是
     *                  false : 否
     * @param data       待发送的消息,必填
     *
     * @throws Exception
     *    IllegalArgumentException 参数无效
     */
    void sendDataBy(String type, String serverId, boolean sendAll, byte[] data) throws Exception;

    /**
     * 发送数据包
     * 由上层业务调用
     *
     * @param type              对端APP类型,必填
     * @param excludeServerIds  对端App的标识符，类型内唯一，可选
     *                          当指定时，则排除这些ID而发送数据
     *                          当不指定时，则是向此类型的消息发送
     * @param sendAll           是否向所有服务器发送
     *                          true  : 是
     *                          false : 否
     * @param data              待发送的消息,必填
     *
     * @throws Exception
     *    IllegalArgumentException 参数无效
     */
    void sendDataBy(String type, String[] excludeServerIds, boolean sendAll, byte[] data) throws Exception;
}
