package com.work.comm.s2s.server.boostrap;

import com.work.comm.s2s.common.IChannelInitializerConfig;
import lombok.Getter;
import lombok.Setter;

import java.util.Properties;

@Setter
@Getter
public class ServerConfig implements IChannelInitializerConfig {
    // 所在应用标识
    // 类型，必填
    private String appType = "";
    // id,必填
    private String appId = "";

    // 监听端口,必填
    private int listenPort;
    // 绑定IP
    // 合法IP，非空
    // 否则不绑定
    private String bindIp;

    // 建立连接IO线程数，默认：2
    // 必须大于0
    private int maxBossThreadNum=2;
    // 请求IO线程数,默认：cpu核数*2
    private int maxWorkerThreadNum=0;

    // TCP连接全队列大小
    // 默认： 128
    private int tcpBacklog = 128;

    // 每个APP维护的最大连接数 */
    private int maxConnNumPerApp = 2;

    /** 拆包配置 */
    private int maxFrameLength = 1024*1024*4;
    private int lengthFieldLength = 4;
    private int lengthFieldOffset = 16;
    private int lengthAdjustment = -20;
    private int initialBytesToStrip = 0;

    /** 心跳检测 */
    // 闲读间隔时间，单位：秒
    // 达到时间间隔则发送心跳数据包
    private int readIdleTimeSec = 5;
    // 允许闲读最大次数
    // 连续达到此次数, 则关闭当前连接
    // 如果是系统标识关闭，则不尝试重连
    private int readIdleMaxNum = 3;
    // ping-pong协议号
    //private int heartbeatReqCode=999;

    /** 客户端身份识别 */
    // WHO协议
    // 收到此协议
    // 则登记对端的身份
    //private int authReqCode=1;
    // 认证超时时间,单位：秒
    private int authTimeoutSec=5;

    // NETTY日志级别
    // 0 = TRACE
    // 1 = DEBUG
    // 2 = INFO
    // 3 = WARN
    // 4 = ERROR
    // 默认是：3
    private int nettyLogLevel=3;

    public static ServerConfig loadFrom(Properties properties){
        ServerConfig config = new ServerConfig();

        if(null != properties) {
            String key = "service.type";
            String keyVal = properties.getProperty(key,"");
            if(null == keyVal || "".equals(keyVal.trim()))
                throw new RuntimeException(String.format("%s not configured!",key));
            config.appType = keyVal.trim();

            key = "service.name";
            keyVal = properties.getProperty(key,"");
            if(null == keyVal || "".equals(keyVal.trim()))
                throw new RuntimeException(String.format("%s not configured!",key));
            config.appId = keyVal.trim();

            key = "service.socket.port";
            keyVal = properties.getProperty(key,"");
            if(null == keyVal || "".equals(keyVal.trim()))
                throw new RuntimeException(String.format("%s not configured!",key));
            Integer temp = tryParseInt(keyVal);
            if(null == temp || temp<=0)
                throw new RuntimeException(String.format("%s invalid: should greater than zero!",key));
            config.listenPort=temp;

            key = "service.socket.ip";
            keyVal = properties.getProperty(key,"");
            if(null != keyVal && !"".equals(keyVal.trim()))
                config.bindIp = keyVal.trim();

            key = "service.maxBossThreadNum";
            keyVal = properties.getProperty(key);
            temp = tryParseInt(keyVal,2);
            config.maxBossThreadNum = Math.max(temp,2);

            key = "service.maxWorkerThreadNum";
            keyVal = properties.getProperty(key);
            temp =  tryParseInt(keyVal,0);
            config.maxWorkerThreadNum = Math.max(temp,0);

            key = "service.socket.balcklog";
            keyVal = properties.getProperty(key);
            temp = tryParseInt(keyVal,128);
            config.tcpBacklog = Math.max(temp,128);

            key = "service.maxConnNumPerApp";
            keyVal = properties.getProperty(key,"2");
            temp = tryParseInt(keyVal,2);
            config.maxConnNumPerApp = Math.max(temp,2);

            key = "service.channel.readIdleTimeout";
            keyVal = properties.getProperty(key);
            temp = tryParseInt(keyVal,5);
            config.readIdleTimeSec = Math.max(temp,5);

            key = "service.channel.readIdleMaxNum";
            keyVal = properties.getProperty(key,"3");
            temp = tryParseInt(keyVal,3);
            config.readIdleMaxNum = Math.max(temp,3);

            /*key = "service.heartbeat.reqCode";
            keyVal = properties.getProperty(key,"");
            if(null == keyVal || "".equals(keyVal.trim()))
                throw new RuntimeException(String.format("%s not configured!",key));
            temp = tryParseInt(keyVal);
            if(null == temp)
                throw new RuntimeException(String.format("%s invalid!",key));
            config.heartbeatReqCode=temp;

            key = "service.auth.reqCode";
            keyVal = properties.getProperty(key,"");
            if(null == keyVal || "".equals(keyVal.trim()))
                throw new RuntimeException(String.format("%s not configured!",key));
            temp = tryParseInt(keyVal);
            if(null == temp)
                throw new RuntimeException(String.format("%s invalid!",key));
            config.authReqCode=temp;*/

            key = "service.auth.timeout";
            keyVal = properties.getProperty(key);
            temp = tryParseInt(keyVal,5);
            config.authTimeoutSec = Math.max(temp,5);

            key = "service.netty.logLevel";
            keyVal = properties.getProperty(key);
            config.nettyLogLevel = tryParseInt(keyVal,3);
            if(config.nettyLogLevel <0 || config.nettyLogLevel>4)
                config.nettyLogLevel = 3;
        }

        return config;
    }

    public static Integer tryParseInt(String value){
        Integer result = null;

        if(null == value || "".equals(value.trim()))
            return result;

        try{
            result = Integer.parseInt(value.trim());
        }catch (Exception ex){
            result = null;
        }

        return result;
    }

    public static int tryParseInt(String value,int defaultVal){
        int result = defaultVal;

        if(null == value || "".equals(value.trim()))
            return result;

        try{
            result = Integer.parseInt(value.trim());
        }catch (Exception ex){
            result = defaultVal;
        }

        return result;
    }

    // NETTY日志级别
    // 0 = TRACE
    // 1 = DEBUG
    // 2 = INFO
    // 3 = WARN
    // 4 = ERROR
    // 默认是：3
    public int getLogLevel(){
        return this.nettyLogLevel;
    }

    // 闲读间隔时间，单位：秒
    // 达到时间间隔则发送心跳数据包
    public int getReadIdleTimeSec(){
        return this.readIdleTimeSec;
    }

    /** 拆包配置 */
    public int getMaxFrameLength(){
        return this.maxFrameLength;
    }
    public int getLengthFieldLength(){
        return this.lengthFieldLength;
    }
    public int getLengthFieldOffset(){
        return this.lengthFieldOffset;
    }
    public int getLengthAdjustment(){
        return this.lengthAdjustment;
    }
    public int getInitialBytesToStrip(){
        return this.initialBytesToStrip;
    }
}
