package com.dzpk.commission.repositories.mysql;

import com.dzpk.commission.repositories.mysql.model.UserBalanceAuditLog;

import java.util.Collections;
import java.util.List;

public interface IUserBalanceAuditDao {

    default void addUserBalanceAuditLog(UserBalanceAuditLog log) {
        addUserBalanceAuditLog(Collections.singletonList(log));
    }

    void addUserBalanceAuditLog(List<UserBalanceAuditLog> logs);
}
