<?xml version="1.0" encoding="UTF-8"?>
<request-mapping>
	<!-- 内部连接的心跳 -->
	<request code="999" classname="com.work.comm.s2s.processor.impl.HeartbeatProcessorImpl"/>
	<!-- 内部连接的认证 -->
	<request code="1" classname="com.work.comm.s2s.processor.impl.WhoProcessorImpl"/>


	<!-- res调用，用于接收游戏广播 -->
	<request code="268" classname="com.i366.processor.server.Processor_268_NotiflyBroadCast"/>

	<!-- res调用，用于接收其它牌局的JP广播 -->
	<request code="718" classname="com.i366.processor.server.Processor_718_JackPotBroadCast"/>

	<!--<request code="201" classname="com.i366.processor.server.Processor_201_BatchRequest"/>
	<request code="203" classname="com.i366.processor.server.Processor_203_ReadExpireRequest"/>
	<request code="204" classname="com.i366.processor.server.Processor_204_BatchControl"/>
	<request code="205" classname="com.i366.processor.server.Processor_205_BatchControl"/>-->
</request-mapping>