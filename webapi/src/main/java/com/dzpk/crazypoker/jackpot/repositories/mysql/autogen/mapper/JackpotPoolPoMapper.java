package com.dzpk.crazypoker.jackpot.repositories.mysql.autogen.mapper;

import com.dzpk.crazypoker.jackpot.repositories.mysql.autogen.model.JackpotPoolPo;
import com.dzpk.crazypoker.jackpot.repositories.mysql.autogen.model.JackpotPoolPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface JackpotPoolPoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jackpot_pool
     *
     * @mbg.generated
     */
    long countByExample(JackpotPoolPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jackpot_pool
     *
     * @mbg.generated
     */
    int deleteByExample(JackpotPoolPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jackpot_pool
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer jackpotId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jackpot_pool
     *
     * @mbg.generated
     */
    int insert(JackpotPoolPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jackpot_pool
     *
     * @mbg.generated
     */
    int insertSelective(JackpotPoolPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jackpot_pool
     *
     * @mbg.generated
     */
    List<JackpotPoolPo> selectByExample(JackpotPoolPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jackpot_pool
     *
     * @mbg.generated
     */
    JackpotPoolPo selectByPrimaryKey(Integer jackpotId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jackpot_pool
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") JackpotPoolPo record, @Param("example") JackpotPoolPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jackpot_pool
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") JackpotPoolPo record, @Param("example") JackpotPoolPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jackpot_pool
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(JackpotPoolPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table jackpot_pool
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(JackpotPoolPo record);
}