package com.dzpk.crazypoker.common.redis.lock;



import com.dzpk.crazypoker.common.utils.LocalIpUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;

/**
 * RedisLockLogger
 *
 * <AUTHOR>
 * @since 2025/6/16
 */
@Component
public class RedisLockLogger {

    @Resource
    RedisLockLoggerDao redisLockLoggerDao;

    /**
     * 开始
     * @param lock 锁
     */
    public void logStart(RedisDistributedLock lock) {
        RedisLockLog entity = new RedisLockLog();
        entity.setName(lock.getConfigName());
        entity.setCode(lock.getConfigCode());
        entity.setConfigExpireTime(lock.getExpireTime());
        entity.setConfigTimeUnit(lock.getTimeUnit().name());
        entity.setConfigRetryCount(lock.getRetryCount());
        entity.setConfigRetryIntervalMs((int) lock.getRetryIntervalMs());
        entity.setLockKey(lock.getLockKey());
        entity.setLockValue(lock.getLockValue());
        entity.setStatus(RedisLockLogStatus.LOCKING); // 获取中
        entity.setThreadId(Thread.currentThread().getName() + "-" + Thread.currentThread().getId());
        entity.setIp(LocalIpUtils.getLocalIP());
        redisLockLoggerDao.insertStart(entity);
    }

    /**
     * 成功获取锁
     * @param lock 锁
     * @param waitTimeMs 等待时间
     */
    public void logSuccess(RedisDistributedLock lock, Integer attempts,long waitTimeMs) {
        RedisLockLog entity = new RedisLockLog();
        entity.setLockKey(lock.getLockKey());
        entity.setLockValue(lock.getLockValue());
        entity.setLockAt(new Timestamp(lock.getLockStartTime()));
        entity.setRealRetryCount(attempts);
        entity.setStatus(RedisLockLogStatus.LOCKED); // 获取成功
        entity.setWaitTimeMs(waitTimeMs);
        entity.setRealGetStartAt(new Timestamp(lock.getLockStartTime() - waitTimeMs));
        entity.setRealGetEndAt(new Timestamp(lock.getLockStartTime()));
        redisLockLoggerDao.updateSuccess(entity);
    }

    /**
     * 获取锁失败
     * @param lock 锁
     * @param errorMsg 错误信息
     * @param waitTimeMs 等待时间
     */
    public void logFailure(RedisDistributedLock lock, Integer attempts,String errorMsg, long waitTimeMs) {
        RedisLockLog entity = new RedisLockLog();
        entity.setLockKey(lock.getLockKey());
        entity.setLockValue(lock.getLockValue());
        entity.setStatus(RedisLockLogStatus.LOCK_FAILED); // 获取失败
        entity.setRealRetryCount(attempts);
        entity.setWaitTimeMs(waitTimeMs);
        entity.setErrorMsg(errorMsg);
        redisLockLoggerDao.updateFailure(entity);
    }

    /**
     * 解锁错误
     * @param lock 锁
     * @param errorMsg 错误消息
     */
    public void logUnlockError(RedisDistributedLock lock, String errorMsg) {
        RedisLockLog entity = new RedisLockLog();
        entity.setLockKey(lock.getLockKey());
        entity.setLockValue(lock.getLockValue());
        entity.setStatus(RedisLockLogStatus.LOCK_TIMEOUT_RELEASED); // 超时释放
        entity.setErrorMsg(errorMsg);
        long unlockTimestamp = lock.getLockStartTime() + lock.getTimeUnit().toMillis(lock.getExpireTime());
        long durationMs = unlockTimestamp - lock.getLockStartTime(); // 实际锁定时长
        entity.setLockTimeMs(durationMs);
        entity.setRealExpireTime(new Timestamp(unlockTimestamp));
        entity.setUnlockAt(new Timestamp(unlockTimestamp));
        redisLockLoggerDao.updateUnlockError(entity);
    }

    /**
     * 解锁成功
     * @param lock 锁
     * @param lockDurationMs 错误消息
     */
    public void logUnlock(RedisDistributedLock lock, long lockDurationMs) {
        RedisLockLog entity = new RedisLockLog();
        entity.setLockKey(lock.getLockKey());
        entity.setLockValue(lock.getLockValue());
        entity.setStatus(RedisLockLogStatus.LOCK_RELEASED); // 正常释放
        entity.setLockTimeMs(lockDurationMs);
        redisLockLoggerDao.updateUnlock(entity);
    }

}
