package com.dzpk.crazypoker.common.rabbitmq.constant;

import com.dzpk.crazypoker.common.constant.BaseEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * OSM - 消息
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum EOsmMessageCode implements BaseEnum {
    //*************** 俱乐部消息 1 - 200 ***************
    CLUB_CREATE_APPLY(1, "申请创建俱乐部", "club.create.apply"),

    //*************** 联盟消息 201 - 400 ***************
    TRIBE_CREATE_APPLY(201, "申请创建联盟", "tribe.create.apply"),
    TRIBE_KICK_OUT_CLUB(202, "联盟踢出俱乐部", "tribe.kick.out.club"),

    //*************** 营销账号消息 401 - 600 ***************
    MARKET_ACCOUNT_LACK_FUND(401, "营销账号余额不足", "market.account.insufficient.fund"),
    MARKET_ACCOUNT_OPERATION_PROMPT(402, "营销账户人工操作提示", "market.account.operation.prompt"),

    //*************** JPB仓消息 801 - 1000 ***************
    JPB_INSUFFICIENT_FUND(801, "JPB仓余额不足", "jpb.insufficient.fund"),
    /**
     * 暂缓
     */
    JPB_OPERATION_PROMPT(801, "JPB仓人工操作提示", "jpb.operation.prompt"),

    //*************** 审计系统消息 1001 - 1200 ***************
    // 2019/5/7 审计推迟，先不做
    AUDIT_WARNING_EXCEPT_EXTRACT_KDOU(1001, "审计系统报警通知（除提豆外）", "audit.warning.except.extract.kdou"),
    AUDIT_EXCEPT_EXTRACT_KDOU_OPERATION_PROMPT(1002, "审计系统提豆人工审核通知", "audit.except.extract.kdou.operation.prompt"),

    //*************** 提豆消息 1201 - 1400 ***************
    WITHDRAW_CHIP_INFO_UPDATE(1201, "提豆信息修改审核", "withdraw.chip.info.update"),
    ;
    private final int code;
    private final String desc;
    private final String message;

    public static EOsmMessageCode valueOf(Integer code) {
        return BaseEnum.valueOf(EOsmMessageCode.class, code);
    }
}
