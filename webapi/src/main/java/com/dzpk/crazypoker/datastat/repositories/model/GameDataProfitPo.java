package com.dzpk.crazypoker.datastat.repositories.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * GameDataProfit
 *  新的个人战绩统计
 * <AUTHOR>
 * @date 2024/11/29
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Document(collection = "game_data_profit")
public class GameDataProfitPo {

    @Id
    private String id;

    @Field("af_rate")
    private Double afRate;

    @Field("allin_hand")
    private Integer allinHand;

    @Field("allin_win_hand")
    private Integer allinWinHand;

    @Field("allin_win_rate")
    private Double allinWinRate;

    @Field("at_id")
    private String atId;

    @Field("avg_bring_in")
    private Integer avgBringIn;

    @Field("avg_earn")
    private Integer avgEarn;

    @Field("tbet_hand")
    private Integer tbetHand;

    @Field("tbet_rate")
    private Double tbetRate;

    @Field("bring_in")
    private Integer bringIn;

    @Field("call_hand")
    private Integer callHand;

    @Field("call_rate")
    private Double callRate;

    @Field("cbet_hand")
    private Integer cbetHand;

    @Field("cbet_rate")
    private Double cbetRate;

    @Field("game_cnt")
    private Integer gameCnt;

    @Field("gold_avg_bring_in")
    private Double goldAvgBringIn;

    @Field("gold_avg_earn")
    private Double goldAvgEarn;

    @Field("gold_bring_in")
    private Integer goldBringIn;

    @Field("gold_earn")
    private Integer goldEarn;

    @Field("gold_game_cnt")
    private Integer goldGameCnt;

    @Field("last_sync_at")
    private Long lastSyncAt;

    @Field("month_af_rate")
    private Double monthAfRate;

    @Field("month_allin_hand")
    private Integer monthAllinHand;

    @Field("month_allin_win_hand")
    private Integer monthAllinWinHand;

    @Field("month_allin_win_rate")
    private Double monthAllinWinRate;

    @Field("month_avg_bring_in")
    private Double monthAvgBringIn;

    @Field("month_avg_earn")
    private Double monthAvgEarn;

    @Field("month_tbet_hand")
    private Integer monthTbetHand;

    @Field("month_tbet_rate")
    private Double monthTbetRate;

    @Field("month_bring_in")
    private Integer monthBringIn;

    @Field("month_call_hand")
    private Integer monthCallHand;

    @Field("month_call_rate")
    private Double monthCallRate;

    @Field("month_cbet_hand")
    private Integer monthCbetHand;

    @Field("month_cbet_rate")
    private Double monthCbetRate;

    @Field("month_game_cnt")
    private Integer monthGameCnt;

    @Field("month_gold_avg_bring_in")
    private Double monthGoldAvgBringIn;

    @Field("month_gold_avg_earn")
    private Double monthGoldAvgEarn;

    @Field("month_gold_bring_in")
    private Integer monthGoldBringIn;

    @Field("month_gold_earn")
    private Integer monthGoldEarn;

    @Field("month_gold_game_cnt")
    private Integer monthGoldGameCnt;

    @Field("month_pfr_hand")
    private Integer monthPfrHand;

    @Field("month_pfr_rate")
    private Double monthPfrRate;

    @Field("month_pool_hand")
    private Integer monthPoolHand;

    @Field("month_pool_rate")
    private Double monthPoolRate;

    @Field("month_pool_win_hand")
    private Integer monthPoolWinHand;

    @Field("month_pool_win_rate")
    private Double monthPoolWinRate;

    @Field("month_raise_hand")
    private Integer monthRaiseHand;

    @Field("month_raise_rate")
    private Double monthRaiseRate;

    @Field("month_showdown_hand")
    private Integer monthShowdownHand;

    @Field("month_showdown_rate")
    private Double monthShowdownRate;

    @Field("month_showdown_win_hand")
    private Integer monthShowdownWinHand;

    @Field("month_showdown_win_rate")
    private Double monthShowdownWinRate;

    @Field("month_total_earn")
    private Integer monthTotalEarn;

    @Field("month_total_hand")
    private Integer monthTotalHand;

    @Field("month_win_hand")
    private Integer monthWinHand;

    @Field("month_win_rate")
    private Double monthWinRate;

    @Field("nickname")
    private String nickname;

    @Field("pfr_hand")
    private Integer pfrHand;

    @Field("pfr_rate")
    private Double pfrRate;

    @Field("pool_hand")
    private Integer poolHand;

    @Field("pool_rate")
    private Double poolRate;

    @Field("pool_win_hand")
    private Integer poolWinHand;

    @Field("pool_win_rate")
    private Double poolWinRate;

    @Field("raise_hand")
    private Integer raiseHand;

    @Field("raise_rate")
    private Double raiseRate;

    @Field("showdown_hand")
    private Integer showdownHand;

    @Field("showdown_rate")
    private Double showdownRate;

    @Field("showdown_win_hand")
    private Integer showdownWinHand;

    @Field("showdown_win_rate")
    private Double showdownWinRate;

    @Field("total_earn")
    private Integer totalEarn;

    @Field("total_hand")
    private Integer totalHand;

    @Field("user_id")
    private String userId;

    @Field("user_random_num")
    private String userRandomNum;

    @Field("week_af_rate")
    private Double weekAfRate;

    @Field("week_allin_hand")
    private Integer weekAllinHand;

    @Field("week_allin_win_hand")
    private Integer weekAllinWinHand;

    @Field("week_allin_win_rate")
    private Double weekAllinWinRate;

    @Field("week_avg_bring_in")
    private Double weekAvgBringIn;

    @Field("week_avg_earn")
    private Double weekAvgEarn;

    @Field("week_tbet_hand")
    private Integer weekTbetHand;

    @Field("week_tbet_rate")
    private Double weekTbetRate;

    @Field("week_bring_in")
    private Integer weekBringIn;

    @Field("week_call_hand")
    private Integer weekCallHand;

    @Field("week_call_rate")
    private Double weekCallRate;

    @Field("week_cbet_hand")
    private Integer weekCbetHand;

    @Field("week_cbet_rate")
    private Double weekCbetRate;

    @Field("week_game_cnt")
    private Integer weekGameCnt;

    @Field("week_gold_avg_bring_in")
    private Double weekGoldAvgBringIn;

    @Field("week_gold_avg_earn")
    private Double weekGoldAvgEarn;

    @Field("week_gold_bring_in")
    private Integer weekGoldBringIn;

    @Field("week_gold_earn")
    private Integer weekGoldEarn;

    @Field("week_gold_game_cnt")
    private Integer weekGoldGameCnt;

    @Field("week_pfr_hand")
    private Integer weekPfrHand;

    @Field("week_pfr_rate")
    private Double weekPfrRate;

    @Field("week_pool_hand")
    private Integer weekPoolHand;

    @Field("week_pool_rate")
    private Double weekPoolRate;

    @Field("week_pool_win_hand")
    private Integer weekPoolWinHand;

    @Field("week_pool_win_rate")
    private Double weekPoolWinRate;

    @Field("week_raise_hand")
    private Integer weekRaiseHand;

    @Field("week_raise_rate")
    private Double weekRaiseRate;

    @Field("week_showdown_hand")
    private Integer weekShowdownHand;

    @Field("week_showdown_rate")
    private Double weekShowdownRate;

    @Field("week_showdown_win_hand")
    private Integer weekShowdownWinHand;

    @Field("week_showdown_win_rate")
    private Double weekShowdownWinRate;

    @Field("week_total_earn")
    private Integer weekTotalEarn;

    @Field("week_total_hand")
    private Integer weekTotalHand;

    @Field("week_win_hand")
    private Integer weekWinHand;

    @Field("week_win_rate")
    private Double weekWinRate;

    @Field("win_hand")
    private Integer winHand;

    @Field("win_rate")
    private Double winRate;



    // 修改个人数据
    @Field("week_game_cnt_edit")
    private Integer weekGameCntEdit;

    @Field("week_total_hand_edit")
    private Integer weekTotalHandEdit;

    @Field("week_pool_rate_edit")
    private Integer weekPoolRateEdit;

    @Field("week_pool_win_rate_edit")
    private Integer weekPoolWinRateEdit;

    @Field("week_allin_win_rate_edit")
    private Integer weekAllinWinRateEdit;

    @Field("week_af_rate_edit")
    private Integer weekAfRateEdit;

    @Field("week_prf_rate_edit")
    private Integer weekPrfRateEdit;

    @Field("week_bet3_rate_edit")
    private Integer weekBet3RateEdit;

    @Field("week_cbet_rate_edit")
    private Integer weekCbetRateEdit;

    @Field("week_tanpai_rate_edit")
    private Integer weekTanpaiRateEdit;

    @Field("month_game_cnt_edit")
    private Integer monthGameCntEdit;

    @Field("month_total_hand_edit")
    private Integer monthTotalHandEdit;

    @Field("month_pool_rate_edit")
    private Integer monthPoolRateEdit;

    @Field("month_pool_win_rate_edit")
    private Integer monthPoolWinRateEdit;

    @Field("month_allin_win_rate_edit")
    private Integer monthAllinWinRateEdit;

    @Field("month_af_rate_edit")
    private Integer monthAfRateEdit;

    @Field("month_prf_rate_edit")
    private Integer monthPrfRateEdit;

    @Field("month_bet3_rate_edit")
    private Integer monthBet3RateEdit;

    @Field("month_cbet_rate_edit")
    private Integer monthCbetRateEdit;

    @Field("month_tanpai_rate_edit")
    private Integer monthTanpaiRateEdit;

    @Field("career_game_cnt_edit")
    private Integer careerGameCntEdit;

    @Field("career_total_hand_edit")
    private Integer careerTotalHandEdit;

    @Field("career_pool_rate_edit")
    private Integer careerPoolRateEdit;

    @Field("career_pool_win_rate_edit")
    private Integer careerPoolWinRateEdit;

    @Field("career_allin_win_rate_edit")
    private Integer careerAllinWinRateEdit;

    @Field("career_af_rate_edit")
    private Integer careerAfRateEdit;

    @Field("career_prf_rate_edit")
    private Integer careerPrfRateEdit;

    @Field("career_bet3_rate_edit")
    private Integer careerBet3RateEdit;

    @Field("career_cbet_rate_edit")
    private Integer careerCbetRateEdit;

    @Field("career_tanpai_rate_edit")
    private Integer careerTanpaiRateEdit;

    @Field("club_room_charge_total")
    private Integer clubRoomChargeTotal = 0;

    @Field("club_room_pl_total")
    private Integer clubRoomPlTotal = 0;
}