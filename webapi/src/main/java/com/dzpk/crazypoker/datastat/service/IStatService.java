package com.dzpk.crazypoker.datastat.service;

import com.dzpk.crazypoker.datastat.service.bean.DataStatCondition;
import com.dzpk.crazypoker.datastat.service.bean.RoomDataBaseBo;


/**
 * 牌局统计抽象接口,具体牌局实现具体统计逻辑,其中各种牌局bean 名称设置为:
 * "IStatService_"+roomPath
 * 如果普通局service名称为 IStatService_61
 */
public interface IStatService {

    /**
     * 根据条件统计数据
     *
     * @param condition
     * @return
     */
    RoomDataBaseBo findByCondition(DataStatCondition condition);

    /**
     * 根据条件加载统计数据
     *
     * @param roomDataBaseBo
     * @param condition
     * @return
     */
    RoomDataBaseBo loadByCondition(RoomDataBaseBo roomDataBaseBo, DataStatCondition condition);

    /**
     * 破隐成功加载数据
     *
     * @param baseBo
     */
    void loadByBreakHide(RoomDataBaseBo baseBo, DataStatCondition condition);

    /**
     * 没有破隐成功加载数据
     *
     * @param baseBo
     */
    void loadByNoBreakHide(RoomDataBaseBo baseBo, DataStatCondition condition);


    RoomDataBaseBo genBo();

}