package com.dzpk.crazypoker.backpack.api.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@ApiModel(value = "背包物品")
public class GameBagGoodsVo {
    /**
     * 门票跳转的比赛id
     */
    @ApiModelProperty(name = "门票跳转的比赛id",
            notes = "门票跳转的比赛id",
            position = 1)
    private Integer gameId;

    /**
     * 物品状态，0未使用 1使用中 2已使用
     */
    @ApiModelProperty(name = "物品状态",
            notes = "物品状态，0未使用 1使用中 2已使用",
            position = 2)
    private Integer propStatus;


    /**
     * 物品id
     */
    @ApiModelProperty(name = "物品id",
            notes = "物品id",
            position = 3)
    private Integer id;

    /**
     * 物品描述，如：获得时间：2017-06-19\n获得赛事：可用于报名官方 MTT 赛事
     */
    @ApiModelProperty(name = "物品描述",
            notes = "物品描述，如：获得时间：2017-06-19\n获得赛事：可用于报名官方 MTT 赛事",
            position = 4)
    private String propDesc;

    /**
     * 物品图片，完整链接
     */
    @ApiModelProperty(name = "物品图片完整链接",
            notes = "物品图片完整链接",
            position = 5)
    private String propIcon;

    /**
     * 物品价值，如100
     */
    @ApiModelProperty(name = "物品价值",
            notes = "物品价值，如100",
            position = 6)
    private Integer propValue;

    /**
     * 是否虚拟物品，未使用，0否 1是
     */
    @ApiModelProperty(name = "是否虚拟物品",
            notes = "是否虚拟物品，未使用，0否 1是",
            position = 7)
    private Integer isVirtual;

    /**
     * 比赛类型
     */
    @ApiModelProperty(name = "比赛类型",
            notes = "比赛类型",
            position = 8)
    private Integer gameType;

    /**
     * 物品名称，如：100元参赛券
     */
    @ApiModelProperty(name = "物品名称",
            notes = "物品名称，如：100元参赛券",
            position = 9)
    private String propName;

    /**
     * 物品类型，1 mtt门票 2 物品
     */
    @ApiModelProperty(name = "物品类型",
            notes = "物品类型，1 mtt门票 2 物品",
            position = 10)
    private Integer propType;
}
