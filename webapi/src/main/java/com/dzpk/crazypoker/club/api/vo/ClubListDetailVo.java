package com.dzpk.crazypoker.club.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * Created by jayce on 2019/4/9
 *
 * <AUTHOR>
 */
@Setter
@Getter
@ApiModel(value = "俱乐部-列表")
public class ClubListDetailVo {

    @ApiModelProperty(name = "是否可以创建俱乐部",
            position = 0,
            notes = "是否可以创建俱乐部<br/>" +
                    "0 = 不可以<br/>" +
                    "1 = 可以")
    private int canCreateClub;

    @ApiModelProperty(name = "是否可以创建房间",
            position = 1,
            notes = "是否可以创建房间<br/>" +
                    "0 = 不可以<br/>" +
                    "1 = 可以")
    private int canCreate;

    private List<ClubListVo> list;

}
