package com.dzpk.crazypoker.room.repositories.mysql;


import com.dzpk.crazypoker.room.api.bean.PumpVo;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface IRoomPumpDao {
    @Select("select * from game_pump")
    List<PumpVo> findPump();

    @Select("select room_path from room_search where room_id=#{roomId}")
    Integer findRoomPathByRoomId(@Param("roomId") int roomId);

    @Insert("insert into club_room_extend(room_id,club_id,tribe_id,crate_time) values(#{roomId}," +
            "#{clubId}, #{tribeId} ,now())")
    int insertClubRoomExtend(@Param("roomId") int roomId, @Param("clubId") int clubId,
                             @Param("tribeId") int tribeId);

    @Select("select club_id from club_members where user_id = #{userId}")
    Integer findClubIdByUserId(@Param("userId") int userId);

    @Select("select tribe_id from tribe_members  where club_id = #{clubId}")
    Integer findTribeIdByClubId(@Param("clubId") int clubId);
}
