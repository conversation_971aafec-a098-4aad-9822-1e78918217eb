package com.dzpk.crazypoker.room.api.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
@ApiModel(description="自动开房计划列表")
public class RoomCreationPlanListVo {

    @ApiModelProperty(
            required = false,
            position = 1,
            notes="计划id"

    )
    @JsonProperty("id")
    private String id;

}
