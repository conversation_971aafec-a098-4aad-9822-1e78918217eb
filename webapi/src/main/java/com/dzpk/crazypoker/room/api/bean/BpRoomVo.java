package com.dzpk.crazypoker.room.api.bean;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
@ApiModel(description="创建牌局&进入牌局-大菠萝专用")
public class BpRoomVo extends NotMatchRoomVo {
    @ApiModelProperty(
            required = false,
            position = 20,
            allowableValues="1,2,3",
            notes="游戏模式：1=普通模式 , 2=血战模式 , 3=血进血出"

    )
    @JsonProperty("mode")
    private Integer mode;

    @ApiModelProperty(
            required = false,
            position = 21,
            allowableValues="0,1",
            notes="竞技模式:0 = 关闭 ,1 = 开启<br/>" +
                    "不需此条件，字段不出现或传null值"

    )
    @JsonProperty("cptton")
    private Integer competitionOn;

    @ApiModelProperty(
            required = false,
            position = 22,
            allowableValues="0,1",
            notes="赖子模式：0 = 关闭 ,1 = 开启"

    )
    @JsonProperty("jkon")
    private Integer jokerOn;

    @ApiModelProperty(
            required = false,
            position = 23,
            allowableValues="0,1",
            notes="限制补充模式:0 = 关闭 ,1 = 开启"
    )
    @JsonProperty("inlton")
    private Integer inLimitOn;

    @ApiModelProperty(
            required = false,
            position = 24,
            notes="补充带入阈值,记分牌小于此值，才允许补充带入"
    )
    @JsonProperty("inltcp")
    private Integer inLimitChip;

    @ApiModelProperty(
            required = false,
            position = 25,
            allowableValues="0,1",
            notes="一战到底模式：0 = 关闭 ,1 = 开启"

    )
    @JsonProperty("tloon")
    private Integer tillOverOn;

    @ApiModelProperty(
            required = false,
            position = 26,
            allowableValues="0,1",
            notes="发牌模式：0 =  同步 ,1 = 顺序"

    )
    @JsonProperty("dlmod")
    private Integer dealMode;

    @ApiModelProperty(
            required = false,
            position = 27,
            notes="最低入局分"
    )
    @JsonProperty("gmcp")
    private Integer gameMinChip;
}
