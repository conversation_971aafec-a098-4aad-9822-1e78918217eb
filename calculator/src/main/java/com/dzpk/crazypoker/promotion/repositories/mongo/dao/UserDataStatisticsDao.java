package com.dzpk.crazypoker.promotion.repositories.mongo.dao;

import com.dzpk.crazypoker.common.mongo.factory.IMongoInstanceFactory;
import com.dzpk.crazypoker.promotion.repositories.mongo.bean.UserDataStatistics;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

import java.util.Date;
import java.util.List;

/**
 * 2019/4/1
 *
 * <AUTHOR>
 */
@Repository
public class UserDataStatisticsDao  {

    @Autowired
    private IMongoInstanceFactory iMongoInstanceFactory;

    private static String USER_DATA_STATISTICS = "user_data_statistics";

    public void insertOrUpdate(Integer userId, Double pl) {
        ReactiveMongoTemplate rTemplate = getReactiveMongoTemplate();
        UserDataStatistics statistics  = selectByUserId(userId);
        // 存在更新，不存在插入
        if(statistics != null) {
            Query query = new Query();
            query.addCriteria(Criteria.where("userId").is(userId));
            Update update = new Update();
            update.inc("totalPl", pl);
            update.set("time", new Date().getTime());
            rTemplate.updateFirst(query, update, UserDataStatistics.class).block();
        } else {
            statistics = new UserDataStatistics();
            statistics.setUserId(userId);
            statistics.setTotalPl(pl);
            statistics.setTime(new Date().getTime());
            rTemplate.save(statistics).block();
        }

    }

    private ReactiveMongoTemplate getReactiveMongoTemplate() {
        return iMongoInstanceFactory.defaultInstance().getTemplate();
    }

    public UserDataStatistics selectByUserId(int userId) {
        ReactiveMongoTemplate rTemplate = getReactiveMongoTemplate();
        Query query = new Query();
        query.addCriteria(Criteria.where("userId").is(userId));
        Mono<UserDataStatistics> mono = rTemplate.findOne(query, UserDataStatistics.class);
        return mono.block();
    }

    public List<UserDataStatistics> selectByUserIds(List<Integer> userIdLists) {
        ReactiveMongoTemplate rTemplate = getReactiveMongoTemplate();
        Query query = new Query();
        query.addCriteria(Criteria.where("userId").in(userIdLists));
        return rTemplate.find(query, UserDataStatistics.class).collectList().block();
    }
}
