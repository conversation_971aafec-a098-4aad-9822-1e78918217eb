package com.dzpk.crazypoker.common.utils;

import com.fasterxml.jackson.annotation.JsonAutoDetect.Visibility;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 */
@Slf4j
public class JsonUtils {
	private static final ObjectMapper MAPPER;

	static {
		MAPPER = new ObjectMapper();
		// 解序列化时不处理未知字段
		MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

		// 仅处理成员变量
		MAPPER.setVisibility(MAPPER.getSerializationConfig().getDefaultVisibilityChecker()
				.withFieldVisibility(Visibility.ANY).withGetterVisibility(Visibility.NONE)
				.withSetterVisibility(Visibility.NONE).withCreatorVisibility(Visibility.NONE)
				.withIsGetterVisibility(Visibility.NONE));

		// 自动转换 Java 8 本地时间
		DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
		DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

		JavaTimeModule module = new JavaTimeModule();
		module.addSerializer(LocalDate.class, new LocalDateSerializer(dateFormatter));
		module.addDeserializer(LocalDate.class, new LocalDateDeserializer(dateFormatter));
		module.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dateTimeFormatter));
		module.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(dateTimeFormatter));
		MAPPER.registerModule(module);
	}

	public ObjectMapper getMapper() {
		return MAPPER;
	}

	public static <T> T read(String json, Class<T> type) {
		try {
			return MAPPER.readValue(json, type);
		} catch (Exception e) {
			log.warn("json read failure, json is {}", json, e);
			throw new RuntimeException(e);
		}
	}

	public static <T> T read(String json, TypeReference type) {
		try {
			return MAPPER.readValue(json, type);
		} catch (Exception e) {
			log.warn("json read failure, json is {}", json, e);
			throw new RuntimeException(e);
		}
	}

	public static String write(Object obj) {
		try {
			return MAPPER.writeValueAsString(obj);
		} catch (Exception e) {
			log.warn("json write failure, obj is {}", obj, e);
			throw new RuntimeException(e);
		}
	}
}
