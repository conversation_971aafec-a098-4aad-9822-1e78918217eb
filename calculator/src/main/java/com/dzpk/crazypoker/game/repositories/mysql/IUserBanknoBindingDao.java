package com.dzpk.crazypoker.game.repositories.mysql;

import com.dzpk.crazypoker.game.repositories.mysql.model.UserBankNoNum;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public interface IUserBanknoBindingDao {

	@Insert({"insert into user_bankno_binding (user_id, bank_no, updated_time)",
			"    value (#{userId}, #{bankNo}, now())",
			"on duplicate key update updated_time = now()"})
	void insertOrUpdate(@Param("userId") Integer userId, @Param("bankNo") String bankNo);

	@Select({"select user_id as userId,count(1) as bindNum from user_bankno_binding where bank_no=#{bankNo} group by user_id"})
	List<UserBankNoNum> countByUserId(@Param("bankNo") String bankNo);
}
