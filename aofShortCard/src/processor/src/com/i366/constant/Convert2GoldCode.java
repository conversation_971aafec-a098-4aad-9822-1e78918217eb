package com.i366.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

/**
 * 积分转换金豆枚举类
 */
@Getter
@AllArgsConstructor
@ToString
public enum Convert2GoldCode {

    ROOM_TIMES_UP_IN_ROOM(1,"房间结束转换,玩家在房间内"),
    ROOM_TIMES_UP_NOT_IN_ROOM(2,"房间结束转换，玩家未在房间内"),
    USER_AHEAD_LEAVE(3,"提前离桌转换"),
    BRINGIN_CHIP(4,"带入积分转换"),
    GAME_RECORD(5,"战绩流水转换")
    ;

    private int code;

    private String desc;
}
