package com.work.comm.s2s.server.boostrap;

import com.work.comm.s2s.handler.ChannelManageHandler;
import com.work.comm.s2s.handler.S2ChannelInitializer;
import com.work.comm.s2s.processor.impl.S2ProcessorFactoryImpl;
import com.work.comm.s2s.server.ClientManager;
import com.work.comm.s2s.handler.S2BusinessHandler;
import io.netty.bootstrap.ServerBootstrap;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelOption;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.util.concurrent.GenericFutureListener;
import lombok.extern.slf4j.Slf4j;

import java.util.Properties;

@Slf4j
public class S2Server implements Runnable {
    /** processor配置文件路径 */
    private static final String eventMappingPath = "wg.requestclassmapping.path";

    // 配置
    private ServerConfig config = null;

    // 建立连接的线程组
    // 通常线程数只需要配置1条
    private EventLoopGroup bossGroup = null;
    // IO线程组
    // 默认配置：cpu核数*2
    private EventLoopGroup workGroup = null;

    // 监听端口的Channel
    private Channel listenChannel = null;

    // 支持的数据处理器
    private S2ProcessorFactoryImpl processorFactory;

    // Netty承接到业务层的Handler
    private S2BusinessHandler businessHandler;

    public S2Server(Properties properties){
        if(null == properties)
            throw new IllegalArgumentException("Configuration required!");

        this.config = ServerConfig.loadFrom(properties);
    }

    @Override
    public void run() {
        try {
            this.bossGroup = new NioEventLoopGroup(this.config.getMaxBossThreadNum());
            this.workGroup = new NioEventLoopGroup(this.config.getMaxWorkerThreadNum());
            ClientManager.initialize(this.workGroup,this.config);
            this.processorFactory = new S2ProcessorFactoryImpl(eventMappingPath,ClientManager.getInstance());
            this.businessHandler = new S2BusinessHandler(this.processorFactory);

            ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(bossGroup, workGroup)
                    .channel(NioServerSocketChannel.class)
                    .childHandler(new S2ChannelInitializer(1L,this.config,
                            new ChannelManageHandler(ClientManager.getInstance()),
                            this.businessHandler))
                    .option(ChannelOption.SO_BACKLOG, this.config.getTcpBacklog())
                    .childOption(ChannelOption.SO_KEEPALIVE, true);

            ChannelFuture future;
            if (null == this.config.getBindIp() || "".equals(this.config.getBindIp().trim()))
                future = bootstrap.bind(this.config.getListenPort());
            else
                future = bootstrap.bind(this.config.getBindIp().trim(), this.config.getListenPort());

            future.addListener(new GenericFutureListener<ChannelFuture>(){
                public void operationComplete(ChannelFuture f) throws Exception{
                    GenericFutureListener<ChannelFuture> self = (GenericFutureListener<ChannelFuture>)this;
                    if(f.isDone()){
                        if(f.isSuccess()){
                            S2Server.this.success(f.channel());
                        }else{
                            S2Server.this.fail(f.isCancelled(),f.cause());
                        }
                    }
                    f.removeListener(self);
                }
            });
        }catch (Exception ex){
            this.fail(false,ex);
        }
    }
    public void shutdown(){
        if(null != this.listenChannel)
            this.listenChannel.close().addListener(new GenericFutureListener<ChannelFuture>(){
                public void operationComplete(ChannelFuture f) throws Exception {
                    GenericFutureListener<ChannelFuture> self = (GenericFutureListener<ChannelFuture>) this;
                    if (f.isDone()) {
                        if (f.isSuccess()) {
                            log.info(String.format("成功关闭内部服务端口: %s",
                                    S2Server.this.formatAsString(S2Server.this.config.getBindIp(),
                                            S2Server.this.config.getListenPort())));
                        } else {
                            log.error(String.format("关闭内部服务端口( %s )失败: %s-%s",
                                    S2Server.this.formatAsString(S2Server.this.config.getBindIp(),
                                            S2Server.this.config.getListenPort()), f.isCancelled(), f.cause()));
                        }
                    }
                    f.removeListener(self);
                }
            });
        if(null != this.bossGroup)
            this.bossGroup.shutdownGracefully();
        if(null != this.workGroup)
            this.workGroup.shutdownGracefully();
    }

    private void fail(boolean isCancelled,Throwable cause){
        this.shutdown();
        throw new RuntimeException(String.format("启动内部服务端口( %s )失败：cancelled=%s,cause=%s",
                this.formatAsString(this.config.getBindIp(),this.config.getListenPort()),
                isCancelled,null==cause?"":cause.getMessage()),cause);
    }
    private void success(Channel channel){
        this.listenChannel = channel;
        log.info(String.format("成功启动内部服务端口: %s",
                this.formatAsString(this.config.getBindIp(),this.config.getListenPort())));
    }
    private String formatAsString(String bindIp,int port){
        if(null == bindIp || "".equals(bindIp))
            bindIp = "*";

        return bindIp+":"+port;
    }
}
