package com.work.comm.io;

import com.dzpk.common.utils.LogUtil;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import org.apache.logging.log4j.Logger;

/**
 * 过滤SLB的RST错误
 */
public class SlbHeartBeatFilter extends ChannelInboundHandlerAdapter{
    /** 日志 */
    private static Logger logger = LogUtil.getLogger(SlbHeartBeatFilter.class);

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception{
        /*if(logger.isDebugEnabled()) {
            Channel incoming = ctx.channel();
            logger.debug("client [" + incoming.remoteAddress() + "] request : " + msg);
        }*/
        ctx.fireChannelRead(msg);
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        /*if(logger.isDebugEnabled()) {
            Channel incoming = ctx.channel();
            logger.debug("client [" + incoming.remoteAddress() + "] caught exception:" + cause.toString(),cause);
        }*/

        ctx.close();
    }
}
