/*
 * $RCSfile: ServerResponse.java,v $
 * $Revision: 1.1  $
 * $Date: 2012-8-8  $
 *
 * Copyright (C) 2005 Bettem, Inc. All rights reserved.
 *
 * This software is the proprietary information of Bettem, Inc.
 * Use is subject to license terms.
 */
package com.work.comm.server.protocal;
/**
 * <p>Title: ServerResponse</p>
 * <p>Description: </p> 
 * <p>Copyright: Copyright (c) 2006</p> 
 * <AUTHOR>
 * @version 1.0
 */
public class ServerResponse {
	private byte[] bytes;
	private int requestCode;
	private int CLOSE_SOCKET = 999;
	
	/**
	 * action中处理完毕后 执行该方法并return该对象则关闭socket连接
	 */
	public void closeSocket() {
		CLOSE_SOCKET = -999;
	}
	/**
	 * 获取重服务器端返回的字节流
	 * @return
	 */
	public byte[] getBytes() {
		return bytes;
	}
	public void setBytes(byte[] bytes) {
		this.bytes = bytes;
	}
	
	/**
	 * 获取requestCode
	 * @return
	 */
	public int getRequestCode() {
		return requestCode;
	}
	/**
	 * 设置requestCode
	 * @param requestCode
	 */
	public void setRequestCode(int requestCode) {
		this.requestCode = requestCode;
	}
	
	/**
	 * 是否关闭socket
	 * @return
	 */
	public boolean isClose() {
		return CLOSE_SOCKET != 999;
	}
}

