package com.work.comm.endpoints.res;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class DistributeSelect {
    /** 【RES服】的唯一标识ID */
    private String resId;

    /**
     * 4种：
     *   usePu=玩家分配数量分发
     *   useIp=玩家登录线路IP分发 ,
     *   useIpAndWhitelist=玩家名单&登陆线路IP分发
     *   usePuAndWhitelist=玩家名单&玩家分配数量分发
     */
    private EDistributePolicy policy;
}
