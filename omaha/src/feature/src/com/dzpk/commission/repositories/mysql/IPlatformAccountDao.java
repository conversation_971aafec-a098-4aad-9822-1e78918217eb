package com.dzpk.commission.repositories.mysql;

import com.dzpk.commission.repositories.mysql.model.PlatformAccountLog;

import java.math.BigDecimal;
import java.sql.SQLException;

/**
 * 系统仓操作
 */
public interface IPlatformAccountDao {

    /**
     * 更新JPB仓的金豆
     * @param amount
     * @return
     * @throws SQLException
     */
    boolean updateJPBAccount(Integer amount) throws SQLException;

    /**
     * 更新系统仓的金豆
     * @param amount
     * @return
     * @throws SQLException
     */
    boolean updateSystemAccount(Integer amount) throws SQLException;

    /**
     * 查询JPB仓余额
     * @return
     * @throws SQLException
     */
    Long queryJPBAccount()throws SQLException;

    /**
     * 查询系统仓余额
     * @return
     * @throws SQLException
     */
    Long querySystemAccount()throws SQLException;

    /**
     * 插入JPB更新记录 来源奥马哈项目
     * @param platformAccountLog
     * @return
     * @throws SQLException
     */
    boolean insertJPBAccountRecordForIntoByOmaha(PlatformAccountLog platformAccountLog) throws SQLException;

    /**
     * 更新分池
     * @param mangzhu
     * @param roomPath
     * @return
     */
    int queryAndUpdateJPAccount(Integer amout,Integer mangzhu, Integer roomPath);
    /**
     * 查询彩金池余额
     * @return
     * @throws SQLException
     */
    BigDecimal queryNewJPBAccount(Integer xiaoMang, Integer roomPath)throws SQLException;
    /**
     * 插入系统仓记录 行为战绩分润，来源奥马哈项目
     * @param platformAccountLog
     * @return
     * @throws SQLException
     */
    boolean insertSystemAccountRecordForGameProfitByOmaha(PlatformAccountLog platformAccountLog) throws SQLException;
    /**
     * 插入系统仓记录 行为JP服务费分润，来源德州项目
     * @param platformAccountLog
     * @return
     * @throws SQLException
     */
    boolean insertJPSystemAccountRecordForGameProfitByDz(PlatformAccountLog platformAccountLog) throws SQLException;
    /**
     * 插入系统仓记录 行为保险分润，来源德州项目
     * @param platformAccountLog
     * @return
     * @throws SQLException
     */
    boolean insertInsuranceSystemAccountRecordForGameProfitByDz(PlatformAccountLog platformAccountLog) throws SQLException;

    /**
     * 查询JP余额
     */
    int queryJPTotalAccount(Integer roomPath)throws SQLException;
    /**
     * 插入系统仓记录 玩家牌局内消费
     * @param platformAccountLog
     * @return
     * @throws SQLException
     */
    boolean insertSystemAccountRecordForGameConsume(PlatformAccountLog platformAccountLog) throws SQLException;

    /**
     * 插入彩池仓贡献记录 来源德州项目
     * @param platformAccountLog
     * @return
     * @throws SQLException
     */
    boolean insertJPBetRecordByOmaha(PlatformAccountLog platformAccountLog) throws SQLException;

    /**
     * 查询跑马灯奖池仓余额
     * @return
     * @throws SQLException
     */
    Long queryHorseRaceLampAccount()throws SQLException;

    /**
     * 更新跑马灯奖池仓余额
     * @return
     * @throws SQLException
     */
    boolean updateHorseRaceLampAccount(Integer amount) throws SQLException;

    /**
     * 插入跑马灯奖池仓更新记录 来源德州项目
     * @param platformAccountLog
     * @return
     * @throws SQLException
     */
    boolean insertHorseRaceLampAccountRecordForIntoByOmaha(PlatformAccountLog platformAccountLog) throws SQLException;
}
