
package com.i366.processor.client;

import java.util.Map;

import com.dzpk.common.utils.LogUtil;
import com.i366.constant.Constant;
import com.work.comm.client.pack.I366ClientPickUtil;
import com.work.comm.client.protocal.BaseRequest;
import com.work.comm.client.protocal.Request;
import org.apache.logging.log4j.Logger;

import com.dzpk.work.Task;
import com.dzpk.work.WorkThreadService;
import com.i366.room.RoomService;
import com.work.comm.io.Handler;


public class Processor_57_AddChips extends Handler {

    private Logger logger = LogUtil.getLogger(Processor_57_AddChips.class);

    @Override
    public byte[] handleRequest(BaseRequest req) {
        Request request = (Request) req;
        logger.debug("Processor_57_AddChips userId is : " + request.getUserId());
        int[][] int2 = {
                {60, I366ClientPickUtil.TYPE_INT_1},        // 玩家座位号
                {61, I366ClientPickUtil.TYPE_INT_1},        //  app渠道 ID，0 王者扑克（默认）1 扑克王国
                {62, I366ClientPickUtil.TYPE_STRING_UTF16},     // 经度
                {63, I366ClientPickUtil.TYPE_STRING_UTF16},    // 纬度
                {64, I366ClientPickUtil.TYPE_STRING_UTF16},     //客户端ip
                {130, I366ClientPickUtil.TYPE_INT_4},       // 玩家添加筹码数
                {131, I366ClientPickUtil.TYPE_INT_4},       // roomPath
                {132, I366ClientPickUtil.TYPE_INT_4},       // roomId
                {133, I366ClientPickUtil.TYPE_INT_4},        // 申请带入类型：0:普通申请；1:特殊申请
                {134, I366ClientPickUtil.TYPE_INT_4},        //  社区ID
                {135, I366ClientPickUtil.TYPE_INT_4},        //  社区管理者ID
                {136, I366ClientPickUtil.TYPE_STRING_UTF16},//  手机机器码
                {137, I366ClientPickUtil.TYPE_STRING_UTF16},//MAC地址 ios可能拿不到
                {138, I366ClientPickUtil.TYPE_INT_4}         //是否模拟器 0否 1是 客户端主动上传
        };

        Map<Integer, Object> map = I366ClientPickUtil.pickAll(request.getBt(), int2);
        int roomPath = (Integer) map.get(131);
        int roomId = (Integer) map.get(132);
        Task task = new Task(Constant.REQ_GAME_RECV_ADD_CHIPS, map, request, roomId, roomPath);

        RoomService.setUserChannel(request, roomId);

        WorkThreadService.submit(roomId, task);
        return null;
    }

}

