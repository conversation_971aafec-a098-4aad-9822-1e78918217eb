package com.dzpk.common.token.service;

import com.dzpk.common.token.dict.ECacheStatus;
import com.dzpk.common.token.vo.ICachedToken;
import com.dzpk.common.token.vo.ZkCachedToken;
import com.dzpk.common.token.zk.TokenWatchService;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.Iterator;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class ZkCacheService implements ICacheService<Integer>, TokenWatchService.IUserTokenCallback {
    /** 日志 */
    private static final Logger logger = LogManager.getLogger(ZkCacheService.class);

    /** 服务实例 */
    private static ZkCacheService _serviceInstance = null;
    public static synchronized ICacheService getInstance(){
        if(null == _serviceInstance) {
            _serviceInstance = new ZkCacheService();
        }
        return _serviceInstance;
    }

    /** token缓存 */
    // token  -> ZkCachedToken
    private Map<String, ZkCachedToken> tokenMap = new ConcurrentHashMap<>();
    // userId -> ZkCachedToken
    private Map<Integer, ZkCachedToken> userMap = new ConcurrentHashMap<>();

    /** 构造函数,私有化 */
    private ZkCacheService(){
        TokenWatchService tokenWatchService = TokenWatchService.getInstance();
        tokenWatchService.setFundCallback(this);
        tokenWatchService.initialize();
    }

    /** 外调方法 */
    /**
     * 获取token对象
     * @param token
     * @return
     */
    public ICachedToken<Integer> getToken(String token){
        if (null == token || "".equals(token.trim()))
            return null;

        token = token.trim();

        StringBuilder logMsg = null;
        if(logger.isDebugEnabled()){
            logMsg = new StringBuilder("==>get token : "+token);
        }

        try {
            //检测是否存在缓存中
            ZkCachedToken tokenInfo;
            // printTokenCache(); // 怀疑token数量过多，比较损耗cpu
            if ((tokenInfo = tokenMap.getOrDefault(token, null)) == null) {
                if(null != logMsg){
                    logMsg.append(" -> Not found in cache");
                }
                return tokenInfo;
            }

            if(null != logMsg){
                logMsg.append(tokenInfo);
            }
            return tokenInfo;
        }finally {
            if(null != logMsg)
                logger.debug(logMsg.toString());
        }
    }

    /**
     * 将token设置为无效
     * @param token
     */
    public void chgInvalid(ICachedToken<Integer> token){
        if(null == token)
            return;

        ZkCachedToken realToken = (ZkCachedToken)token;
        changeStatus(realToken, ECacheStatus.invalid);
    }

    /**
     * 创建token对象
     * @param token
     * @param userInfo
     * @param deviceId
     * @return
     */
    public ICachedToken<Integer> addToken(String token, Integer userInfo, String deviceId){
        if(null == token || null == userInfo || userInfo<=0)
            return null;

        token = token.trim();
        if(null != deviceId)
            deviceId = deviceId.trim();
        else
            deviceId = "";

        ZkCachedToken newToken = new ZkCachedToken(token);
        newToken.setVersion(0);
        newToken.setDeviceId(deviceId);
        newToken.setUserId(userInfo);

        TokenWatchService.getInstance().updateToken(newToken.getUserInfo(), ZkCachedToken.getValue(newToken));

        this.tokenMap.put(token,newToken);
        this.cacheTokenByUser(newToken);

        return newToken;
    }

    /**
     * 修改token状态
     * 有效修改路径：
     * loading -> valid
     * any     -> invalid
     * @param token
     */
    private void changeStatus(ZkCachedToken token, ECacheStatus status) {
        if (null == token || null == status)
            return;

        try {
            synchronized (token) {
                //invalid终结状态
                if (token.getStatus() == ECacheStatus.invalid)
                    return;

                //valid只能修改为invalid
                if (token.getStatus() == ECacheStatus.valid &&
                        status != ECacheStatus.invalid)
                    return;

                //状态相同，无需再修改
                if (token.getStatus() == status)
                    return;

                token.setStatus(status);
            }
        }finally {
            //如果已经失效
            if(token.getStatus() == ECacheStatus.invalid){
                TokenWatchService.getInstance().deleteToken(token.getUserInfo(),token.getToken(),token.getVersion());
            }
        }
    }

    /** zk callback */
    /**
     * Token删除通知
     * @param userId      用户ID
     * @param token       token值
     * @param version     版本
     */
    public void delete(int userId, String token, int version){
        StringBuilder traceLog = null;
        if(logger.isDebugEnabled()){
            traceLog = new StringBuilder(String.format("ZK Deleted-event : userId=%s , version=%s , token=%s",
                    userId,version,token));
        }
        try {
            if (null == token || "".equals(token)) {
                if(null != traceLog){
                    traceLog.append(" -> token值为空！");
                }
                return;
            }

            ZkCachedToken changedToken = ZkCachedToken.from(token.trim());
            if (null == changedToken ||
                    changedToken.getToken() == null ||
                    "".equals(changedToken.getToken())) {
                if(null != traceLog){
                    traceLog.append(" -> token值无效！");
                }
                return;
            }

            changedToken.setVersion(version);
            changedToken.setUserId(userId);

            ZkCachedToken cachedToken = this.tokenMap.remove(changedToken.getToken());
            if(null != traceLog){
                traceLog.append("\r\ncached-token : " + cachedToken);
                traceLog.append("\r\nnotified-token : " + changedToken);
            }
            this.clearUserToken(cachedToken);
        }finally {
            if(null != traceLog){
                logger.debug(traceLog.toString());
            }
        }
    }

    /**
     * Token变化通知
     * @param userId      用户ID
     * @param token       token值
     * @param version     版本
     */
    public void update(int userId, String token, int version){
        StringBuilder traceLog = null;
        if(logger.isDebugEnabled()){
            traceLog = new StringBuilder(String.format("ZK Updated-event : userId=%s , version=%s , token=%s",
                    userId,version,token));
        }
        try {
            if (null == token || "".equals(token)) {
                if(null != traceLog){
                    traceLog.append(" -> token值为空！");
                }
                return;
            }

            ZkCachedToken changedToken = ZkCachedToken.from(token.trim());
            if (null == changedToken ||
                    changedToken.getToken() == null ||
                    "".equals(changedToken.getToken())) {
                if(null != traceLog){
                    traceLog.append(" -> token值无效！");
                }
                return;
            }

            changedToken.setVersion(version);
            changedToken.setUserId(userId);

            logger.debug("准备---获取缓存中的token对象。。。。");
            ZkCachedToken oldToken = this.tokenMap.get(changedToken.getToken());
            logger.debug("获取缓存中的token对象。。。。");
            if(null != traceLog){
                traceLog.append("\r\ncached-token : " + oldToken);
                traceLog.append("\r\nnotified-token : " + changedToken);
            }
            if (oldToken == null) {
                this.tokenMap.put(changedToken.getToken(), changedToken);
                this.cacheTokenByUser(changedToken);
            } else if (changedToken.getVersion() > oldToken.getVersion() &&
                    oldToken.getUserInfo() == userId) {
                this.tokenMap.put(changedToken.getToken(), changedToken);
                this.cacheTokenByUser(changedToken);
            }
        }catch (Exception ex){
            logger.error(ex);
        }finally{
            if(null != traceLog){
                logger.debug(traceLog.toString());
            }
        }
    }

    /** 私有方法 */
    private void printTokenCache(){
        if(logger.isDebugEnabled()) {
            StringBuilder traceLogs = new StringBuilder("Current tokens in Cache as bellow:");
            Iterator<String> it = tokenMap.keySet().iterator();
            while (it.hasNext()) {
                String token = it.next();
                traceLogs.append(String.format("\r\n%s -> %s ",token,tokenMap.get(token)));
            }
            logger.debug(traceLogs.toString());
        }
    }

    private void cacheTokenByUser(ZkCachedToken newToken){
        if(null == newToken ||
                null == newToken.getToken() ||
                "".equals(newToken.getToken().trim()) ||
                null == newToken.getUserInfo() ||
                newToken.getUserInfo()<=0)
            return;

        synchronized (this.userMap) {
            //根据用户ID查找之前的token，移除
            ZkCachedToken userOldToken = this.userMap.get(newToken.getUserInfo());
            if (null != userOldToken &&
                    !newToken.getToken().equals(userOldToken.getToken())) {
                this.tokenMap.remove(userOldToken.getToken());
            }
            this.userMap.put(newToken.getUserInfo(), newToken);
        }
    }

    private void clearUserToken(ZkCachedToken oldToken){
        if(null == oldToken ||
                null == oldToken.getUserInfo() ||
                oldToken.getUserInfo()<=0)
            return;

        synchronized (this.userMap) {
            //根据用户ID查找之前的token，移除
            ZkCachedToken userOldToken = this.userMap.get(oldToken.getUserInfo());
            if (oldToken == userOldToken) {
                this.userMap.remove(oldToken.getUserInfo());
            }
        }
    }
}
