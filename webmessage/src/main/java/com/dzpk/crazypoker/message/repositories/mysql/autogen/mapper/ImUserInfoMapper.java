package com.dzpk.crazypoker.message.repositories.mysql.autogen.mapper;

import com.dzpk.crazypoker.message.repositories.mysql.autogen.model.ImUserInfo;
import com.dzpk.crazypoker.message.repositories.mysql.autogen.model.ImUserInfoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ImUserInfoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table im_user_info
     *
     * @mbg.generated
     */
    long countByExample(ImUserInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table im_user_info
     *
     * @mbg.generated
     */
    int deleteByExample(ImUserInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table im_user_info
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Integer userId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table im_user_info
     *
     * @mbg.generated
     */
    int insert(ImUserInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table im_user_info
     *
     * @mbg.generated
     */
    int insertSelective(ImUserInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table im_user_info
     *
     * @mbg.generated
     */
    List<ImUserInfo> selectByExample(ImUserInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table im_user_info
     *
     * @mbg.generated
     */
    ImUserInfo selectByPrimaryKey(Integer userId);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table im_user_info
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") ImUserInfo record, @Param("example") ImUserInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table im_user_info
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") ImUserInfo record, @Param("example") ImUserInfoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table im_user_info
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(ImUserInfo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table im_user_info
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(ImUserInfo record);
}