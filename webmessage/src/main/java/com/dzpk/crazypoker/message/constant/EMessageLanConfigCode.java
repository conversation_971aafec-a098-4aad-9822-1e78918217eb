package com.dzpk.crazypoker.message.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 对应国际化配置文件的代号枚举类
 * Created by jayce on 2019/4/8
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum EMessageLanConfigCode {

    CLUB_CREATE_SUCCESS(1,"club.create.success","创建俱乐部成功消息",1,false),
    CLUB_CREATE_FAIL(2,"club.create.fail","创建俱乐部失败消息",1,false),
    CLUB_CHANGE_MEMBER_LIMIT(3,"club.change.member.limit","俱乐部被修改成员上限",1,false),
    CLUB_CHANGE_PROFIT(4,"club.change.profit","俱乐部占成比例修改",1,false),
    CLUB_FUND_GAME_PROFIT(5,"club.fund.game.profit","俱乐部牌局收益",2,true),
    CLUB_CHANGE_TRIBE(6,"club.change.tribe","俱乐部被调整联盟",1,false),
    CLUB_CHANGE_PAY_TYPE_S(7,"club.change.pay.type.success","俱乐部申请更改交易模式成功",0,false),
    CLUB_CHANGE_PAY_TYPE_F(8,"club.change.pay.type.fail","俱乐部申请更改交易模式失败",0,false),
    CLUB_CHANGE_PAY_TYPE(9,"club.change.pay.type","俱乐部交易模式被更改",1,false),
    CLUB_CLOSED_NOTIFY(10,"club.closed.notify","俱乐部被关闭",1,false),
    CLUB_JOIN_TRIBE_REQUEST_S(11,"club.join.tribe.success","俱乐部加入联盟申请成功",1,false),
    CLUB_JOIN_TRIBE_REQUEST_F(12,"club.join.tribe.fail","俱乐部加入联盟申请失败",1,false),
    CLUB_MEMBER_LIMIT_NOTIFY(13,"club.member.limit.notify","俱乐部成员上限已满通知",0,false),
    CLUB_MEMBER_CANT_CHARGE(14,"club.member.cant.charge","俱乐部玩家无法充值",0,false),
    CLUB_GAME_PROFIT(15,"club.game.profit","战绩返佣",1,false),
    CLUB_UPDATE_RANDOM_ID(16,"club.update.random.id","俱乐部修改显性id",1,false),

    TRIBE_CREATE_SUCCESS(201,"tribe.create.success","创建联盟成功",1,false),
    TRIBE_CREATE_FAIL(202,"tribe.create.fail","创建联盟失败",1,false),
    TRIBE_JOIN_NEW_CLUB(203,"tribe.join.new.club","联盟新加俱乐部",2,false),
    TRIBE_REMOVE_CLUB(204,"tribe.remove.club","联盟踢出俱乐部",1,false),
    TRIBE_CHANGE_MEMBER_LIMIT(205,"tribe.change.member.limit","联盟上限修改",1,false),
    TRIBE_RECIVE_JOIN_REQUEST(206,"tribe.recive.club.join.request","联盟收到俱乐部加入申请",2,false),
    TRIBE_CHANGE_PROFIT(207,"tribe.change.profit","联盟占成比例修改",1,false),
    TRIBE_CHANGE_MEMBER(208,"tribe.change.member","联盟下属俱乐部被移除或被调整",2,false),
    TRIBE_CLOSED_NOTIFY(209,"tribe.closed.notify","联盟被关闭",1,false),
    TRIBE_FUND_GAME_PROFIT(210,"tribe.fund.game.profit","联盟牌局收益",2,true),
    TRIBE_UPDATE_RANDOM_ID(211,"tribe.update.random.id","联盟修改显性id",1,false),

    SYSTEM_NOTICE(401,"system.notice","系统公告",1,false),
    SYSTEM_VINDICATE(402,"system.vindicate","系统维护",1,false),
    SYSTEM_USER_FORZEN(403,"system.user.forzen","账号被冻结",1,false),
    //404 不需要文案
    SYSTEM_REPORT(405,"system.report","投诉反馈",0,false),
    //406  客户端不需要显示，只需要推送
    SYSTEM_USER_SEATED_NOTIFY(406,"system.user.seated.notify","特定名单用户坐下通知",3,false),
    SYSTEM_DEAL_RESULT_NOTIFY(407,"system.deal.result.notify","伙牌投诉处理通知",0,false),
    SYSTEM_DEAL_RESULT_CUSTOM_NOTIFY(408,"system.deal.result.custom.notify","伙牌投诉处理自定义通知",1,false),

    MONEY_KDOU_ACCOUNT(601,"money.kdou.account","充豆到账",1,true),
    MONEY_KDOU_GIVE_FUND(602,"money.kdou.give.fund","被发放俱乐部基金（可提）",2,true),
    MONEY_KDOU_EXTRACT_S(603,"money.kdou.extract.success","提豆成功消息",1,true),
    MONEY_KDOU_TRANSFER_S(604,"money.kdou.transfer.success","转豆成功消息",2,true),
    MONEY_KDOU_EXTRACT_REQUEST(605,"money.kdou.extract.request","提豆申请消息",2,true),
    MONEY_KDOU_RECEIVE(606,"money.kdou.receive","被转豆",2,true),
    MONEY_CHANGE_EXTRACT_S(607,"money.change.extract.success","申请修改提豆信息成功",0,true),
    MONEY_CHANGE_EXTRACT_F(608,"money.change.extract.fail","申请修改提豆信息失败",0,true),
//    MONEY_CHANGE_FUND_NOT_ENOUGH_NOTIFY(609,"money.charge.fund.not.enough.notify","玩家充值，俱乐部基金仓余额不足通知",2),
//    MONEY_FUND_NOT_ENOUGH_NOTIFY(610,"money.fund.not.enough.notify","基金仓余额不足通知",0),
    MONEY_EXTRACT_KDOU_REQUEST_FAIL(611,"money.kdou.extract.fail.notify","提豆申请不通过消息",3,true),
    MONEY_KDOU_CHARGE_FAIL(612,"money.kdou.account.fail","充豆失败",0,true),
    MONEY_EXTRACT_KDOU_FAIL(613,"money.kdou.extract.fail","提豆失败",0,true),
    MONEY_AGENCY_NO_ENOUGH(614,"money.agency.charge.no.enough","代充充值渠道账户金豆不足",0,true),
    MONEY_SYSTEM_GIVE(615,"money.system.give","系统发放金豆（手工充豆）",1,true),
    MONEY_SKY_ACTIVITY_GIVE(616,"money.sky.activity.give","天降红包活动",1,true),
    MONEY_KDOU_GIVE_FUND_N_E(617,"money.kdou.give.fund.n.e","被发放俱乐部基金(不可提)",2,true),
    MONEY_PMD_REWARD(618,"money.pmd.reward","跑马灯中奖",1,true),

    BAG_GET_PRIZE(801,"bag.get.prize","获得奖品",1,false),
    BAG_GET_TICKET(802,"bag.get.ticket","获得门票",1,false),

    SHARE_KDOU_PROFIT(1001,"share.kdou.profit","分享赚金豆",1,true),

    //************Send Aws Sns 7000 - 7200*************
    SNS_CUSTOM(7000,"sns.custom", "發送自定信息",0,true),
    SNS_PLAYER_TURN(7001,"sns.player.turn", "已輪到你的回合",0,true),
    //************Send Aws Sns 7000 - 7200*************
    ;

    //对应EMessageCode的消息代号
    private Integer num;

    //对应国际化配置文件的code
    private String code;

    //描述
    private String desc;

    //消息对应参数数量
    private Integer paramNum;

    //需要刷新
    private boolean needRefresh;

}
