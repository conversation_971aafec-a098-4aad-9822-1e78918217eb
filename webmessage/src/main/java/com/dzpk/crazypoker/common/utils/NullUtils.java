package com.dzpk.crazypoker.common.utils;

public class NullUtils {
    private final static Integer ZERO = 0;

    private final static String EMPTY_STRING = "";

    public static Integer intNull2Zero(Integer value) {
        if (value == null) {
            return ZERO;
        }
        return value;
    }

    public static String strNull2Empty(String value) {
        if (value == null) {
            return EMPTY_STRING;
        }

        return value;
    }


    public static String stringNull2Zero(String value) {
        if (value == null) {
            return EMPTY_STRING;
        }

        return value;
    }

    public static String arrayIndex(String[] strs, int index) {
        if (strs != null && index < strs.length && strs[index] != null) {
            return strs[index];
        }
        return "";
    }

    public static int string2int(String[] strs, int index) {
        if (strs != null && index < strs.length && strs[index] != null) {
            String temp = strs[index].trim();
            if (temp.length() != 0) {
                return Integer.parseInt(strs[index]);
            }
        }

        return 0;
    }
}
