package com.dzpk.crazypoker.common.dozer;

import org.dozer.Mapper;

import java.util.*;

public final class BeanUtil {
    /**
     * 通过spring注入Mapper工厂实例
     */
    private Mapper mapper;
    public  Mapper mapper() {
        return mapper;
    }

    public BeanUtil(Mapper mapper){
        this.mapper=mapper;
    }

    public  <T> T map(Object source, Class<T> destinationClass) {
        if (source == null) {
            return null;
        }
        return mapper().map(source, destinationClass);
    }

    public  <T> List<T> map(List<?> source, Class<T> clz) {
        List<T> target = new ArrayList<T>();
        if(null == source)
            return target;

        for (Object o : source) {
            T to = map(o, clz);
            target.add(to);
        }
        return target;
    }

    public  <T, V> Map<T, V> map(Map<?, ?> source, Class<T> keyClz, Class<V> valueClz) {
        Map<T, V> target = new HashMap<T, V>();
        if(null == source)
            return target;

        Set<?> entrySet = source.keySet();
        for (Object sourcekey : entrySet) {
            T key = map(sourcekey, keyClz);
            V value = map(source.get(sourcekey), valueClz);
            target.put(key, value);
        }
        return target;
    }

    public  <T, V> Map<T, V> map(Map<T, ?> source, Class<V> valueClz) {
        Map<T, V> target = new HashMap<T, V>();
        if(null == source)
            return target;

        Set<T> entrySet = source.keySet();
        for (T sourcekey : entrySet) {
            V value = map(source.get(sourcekey), valueClz);
            target.put(sourcekey, value);
        }
        return target;
    }
}
