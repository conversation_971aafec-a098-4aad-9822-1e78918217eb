package com.dzpk.crazypoker.common.web.token.algorithm.impl;


import com.dzpk.crazypoker.common.web.token.algorithm.IToken;

/**
 * 用户ID及当前登陆的设备ID生成token
 * 1. 拼接字符串格式： user_id + "&&" + deviceId(6 ~ 10)
 * 2. base64算法对字符串进行编码
 * 3. md5对拼接字符串进行签名,抽取：6位
 * 5. 拼接成：
 */
public class Base64TokenImpl extends AbtractTokenImpl implements IToken {
    /**
     * token header
     * userId&&deviceId&&timestamp
     */
    private static final String CONTENT_PATTERN = "%s%s%s%s%s";

    /**
     * payload字段名
     */
    private static final String PAYLOAD_FD_SEPERATOR="&&";
    private static final int PAYLOAD_FD_NUMBER = 3;

    public Base64TokenImpl(String aesSecretKey) {
        this(aesSecretKey,null);
    }
    public Base64TokenImpl(String aesSecretKey, String charsetString) {
        super(aesSecretKey,charsetString);
    }

    /**
     * 生成token
     * @param   userId     用户ID，必填
     * @param   deviceId   设备ID
     * @return
     */
    public String token(int userId,String deviceId){
        String content = String.format(CONTENT_PATTERN,userId,PAYLOAD_FD_SEPERATOR,
                this.getDeviceID(deviceId),PAYLOAD_FD_SEPERATOR,System.currentTimeMillis());
        String base64Str = this.toBase64(content.getBytes(this.charset));
        String signStr = this.getSign(content);
        return base64Str+signStr;
    }

    /**
     * 校验Token
     * @param   token          ticket,必填
     * @param   reqUserId      当前使用此token的用户ID，可选，
     *                         >0则需校验与此token生成的userID是否匹配
     * @param   reqDeviceId    当前使用此token请求的设备ID，可选
     *                         不为空，则需要校验与此token生成的设备ID是否匹配
     * @return  userId
     *     <=0 表示失败
     *    其他 表示成功，对应的用户ID
     */
    public int verify(String token, Integer reqUserId, String reqDeviceId){
        int userId = 0;

        if(null == token || "".equals(token.trim()))
            return userId;

        token = token.trim();
        if(token.length()<=6)
            return userId;

        try {
            String signStr = token.substring(token.length()-6);
            String base64Str = token.substring(0,token.length()-6);
            String content = new String(this.fromBase64(base64Str),this.charset);
            String genSign = this.getSign(content);
            if(!signStr.equals(genSign)){
                return userId;
            }

            String[] fields = content.split(PAYLOAD_FD_SEPERATOR);
            if(fields.length!=PAYLOAD_FD_NUMBER)
                return userId;

            try {
                userId = Integer.parseInt(fields[0]);
            }catch (Exception ex){
                return userId;
            }
            if(!fields[1].equals(this.getDeviceID(reqDeviceId)))
                return userId;
        }catch (Exception ex){
            ex.printStackTrace();
        }

        return userId;
    }

    /**
     * 设备ID中抽取第6位到第10位
     * 不足10位，则是到最后
     * 不足6位，则返回""
     * @param deviceId
     * @return
     */
    private String getDeviceID(String deviceId) {
        if(null == deviceId)
            return "";
        deviceId = deviceId.trim();
        if("".equals(deviceId) || deviceId.length()<6)
            return "";
        if(deviceId.length()<10)
            return deviceId.substring(6);

        return deviceId.substring(6,10+1);
    }

    /**
     * 对数据进行MD5签名，然后抽取以下位置数据：
     * 18 ，3 ，20 ，30 ，0，26
     * @param content
     * @return
     */
    private String getSign(String content){
        if(null == content)
            return "";
        content = content.trim();
        if("".equals(content))
            return content;

        String md5Sign = this.md5(content);
        StringBuilder sb = new StringBuilder();
        sb.append(md5Sign.charAt(18));
        sb.append(md5Sign.charAt(3));
        sb.append(md5Sign.charAt(20));
        sb.append(md5Sign.charAt(30));
        sb.append(md5Sign.charAt(0));
        sb.append(md5Sign.charAt(26));
        return sb.toString();
    }

    public static  void main(String[] args){
        //生成AES密钥
        String AESSecret = AbtractTokenImpl.generateKey();
        System.out.println("AES Secret  :  " + AESSecret);
        AESSecret = AbtractTokenImpl.generateKey();
        System.out.println("AES Secret  :  " + AESSecret);
        AESSecret = AbtractTokenImpl.generateKey();
        System.out.println("AES Secret  :  " + AESSecret);
        AESSecret = AbtractTokenImpl.generateKey();
        System.out.println("AES Secret  :  " + AESSecret);

        String secretKey = "BFF0200C8FDB3EFC50D1B823FEB87512";
        int userId1 = 159636;
        int userId2 = 159637;

        String deviceId1 = "862873024279306";
        String deviceId2 = "860954024415397";

        IToken token = new Base64TokenImpl(AESSecret);

        // 1. 不同的参数生成的key是否相同：不同
        String token1 = token.token(userId1,deviceId1);
        System.out.println("UserID="+ userId1 + " , DeviceID="+deviceId1 + " -> token = " + token1);

        String token2 = token.token(userId2,deviceId2);
        System.out.println("UserID="+ userId2 + " , DeviceID="+deviceId2 + " -> token = " + token2);

        //2. 能否正常校验
        System.out.println("UserID="+ userId1 + " , DeviceID="+deviceId1 + "-> user_id="+ token.verify(token1,null,deviceId1));
        System.out.println("UserID="+ userId2 + " , DeviceID="+deviceId2 + "-> user_id="+ token.verify(token2,null,deviceId2));

        //3. 不同deviceID是否能校验
        System.out.println("UserID="+ userId1 + " , DeviceID="+deviceId1 + "-> user_id="+ token.verify(token1,null,deviceId2));
    }
}
