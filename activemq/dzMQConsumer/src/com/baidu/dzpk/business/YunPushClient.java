package com.baidu.dzpk.business;

import java.util.List;

import org.apache.logging.log4j.Logger;
import org.json.JSONObject;

import com.baidu.yun.core.log.YunLogEvent;
import com.baidu.yun.core.log.YunLogHandler;
import com.baidu.yun.push.auth.PushKeyPair;
import com.baidu.yun.push.client.BaiduPushClient;
import com.baidu.yun.push.constants.BaiduPushConstants;
import com.baidu.yun.push.exception.PushClientException;
import com.baidu.yun.push.exception.PushServerException;
import com.baidu.yun.push.model.AddDevicesToTagRequest;
import com.baidu.yun.push.model.AddDevicesToTagResponse;
import com.baidu.yun.push.model.CreateTagRequest;
import com.baidu.yun.push.model.CreateTagResponse;
import com.baidu.yun.push.model.DeleteDevicesFromTagRequest;
import com.baidu.yun.push.model.DeleteDevicesFromTagResponse;
import com.baidu.yun.push.model.DeleteTagRequest;
import com.baidu.yun.push.model.DeleteTagResponse;
import com.baidu.yun.push.model.DeviceInfo;
import com.baidu.yun.push.model.PushMsgToAllRequest;
import com.baidu.yun.push.model.PushMsgToAllResponse;
import com.baidu.yun.push.model.PushMsgToSingleDeviceRequest;
import com.baidu.yun.push.model.PushMsgToSingleDeviceResponse;
import com.baidu.yun.push.model.PushMsgToTagRequest;
import com.baidu.yun.push.model.PushMsgToTagResponse;

/**
 * 百度云推送客户端
 * <AUTHOR>
 * @email  <EMAIL>
 * @date   2016年11月22日
 *
 */
public class YunPushClient {
    private static final Logger logger = com.work.comm.util.LogUtil.getLogger(YunPushClient.class);

    /*1. 创建PushKeyPair用于app的合法身份认证
     * 
     */
    private static String androidApiKey    = "jKvcipIGGpUTTkMXeqxSTj8g";
    private static String androidSecretKey = "LAIGtD5EozWVUD6MTCPCBsvI80E9PFiG";
    
    private static String iosApiKey    = "5rU0GABYHDcexf07UdOGHF3v";
    private static String iosSecretKey = "7ui9MxVqhYAq4c7yoNZumY0F9RdOcf1o";
    
    private static PushKeyPair androidPair = new PushKeyPair(androidApiKey, androidSecretKey);
    private static PushKeyPair iosPair     = new PushKeyPair(iosApiKey, iosSecretKey);
    // 2. 创建BaiduPushClient，访问SDK接口
    private static BaiduPushClient androidClient = new BaiduPushClient(androidPair, BaiduPushConstants.CHANNEL_REST_URL);
    private static BaiduPushClient iosClient     = new BaiduPushClient(iosPair, BaiduPushConstants.CHANNEL_REST_URL);
    private static BaiduPushClient pushClients[] = {androidClient, iosClient}; 
    
    static {
        for (BaiduPushClient client : pushClients) {
            // 3. 注册YunLogHandler，获取本次请求的交互信息
            client.setChannelLogHandler (new YunLogHandler () {           
                public void onHandle (YunLogEvent event) {
                    logger.info(event.getMessage());
                }
            });
        }
    }
    
    
    /**
     * 向单个设备推送消息
     * @param channelId 必须为客户端初始化成功之后返回的channelId，默认null
     * @param msgExpires 相对于当前时间的消息过期时间，单位为秒. 取值：(0, 86400 x 7]，默认值为3600 x 5
     * @param msgType 消息类型. 0：透传消息 1：通知 默认值为0
     * @param message 消息内容，json格式(http://push.baidu.com/doc/restapi/msg_struct)
     * @param type 0: Android 1: IOS
     * @return
     */
    public static JSONObject pushToSingleDevice(String channelId, Integer msgExpires, Integer msgType, 
            String message, int type) {
        int status = 0;
        JSONObject object = new JSONObject();
        try {
            PushMsgToSingleDeviceRequest request = new PushMsgToSingleDeviceRequest()
            .addChannelId(channelId)
            .addMsgExpires(msgExpires)
            .addMessageType(msgType)
            .addMessage(message)
            .addTopicId("topic");
            if (1 == type) {
                request.setDeployStatus(2); // 1：开发状态 2：生产状态
                request.setDeviceType(4);
            } else {
                request.setDeviceType(3);
            }
            PushMsgToSingleDeviceResponse response = pushClients[type].pushMsgToSingleDevice(request);
            
            object.put("msgId", response.getMsgId());
            object.put("sendTime", response.getSendTime());
            logger.info("msgId: " + response.getMsgId() + ",sendTime: " + response.getSendTime());
        } catch (PushClientException e) {
            status = 1;
            object.put("exception", e.toString());
        } catch (PushServerException e) {
            status = 2;
            object.put("exception", String.format(
                        "requestId: %d, errorCode: %d, errorMsg: %s",
                        e.getRequestId(), e.getErrorCode(), e.getErrorMsg()));
        }
        object.put("status", status);
        return object;
    }
    

    /**
     * 推送消息给所有设备，即广播推送
     * @param msgExpires 相对于当前时间的消息过期时间，单位为秒.取值：(0, 86400 x 7]，默认值为3600 x 5
     * @param msgType 消息类型. 0：透传消息 1：通知 默认值为0
     * @param message 消息内容，json格式(http://push.baidu.com/doc/restapi/msg_struct)
     * @param sendTime 待发送时间戳，用于定时消息推送. 必须在当前时间60s以外，1年（31536000s）以内，默认为null
     * @param type 0: Android 1: IOS 
     * @return
     */
    public static JSONObject pushToAll(Integer msgExpires, Integer msgType, String message, Long sendTime,
            int type) {
        int status = 0;
        JSONObject object = new JSONObject();
        try {
            PushMsgToAllRequest request = new PushMsgToAllRequest()
            .addMsgExpires(msgExpires)
            .addMessageType(msgType)
            .addMessage(message);
//            .addSendTime(sendTime);
            PushMsgToAllResponse response = pushClients[type].pushMsgToAll((PushMsgToAllRequest)request);
            
            object.put("msgId", response.getMsgId());
            object.put("sendTime", response.getSendTime());
        } catch (PushClientException e) {
            status = 1;
            object.put("exception", e.toString());
        } catch (PushServerException e) {
            status = 2;
            object.put("exception", String.format(
                        "requestId: %d, errorCode: %d, errorMsg: %s",
                        e.getRequestId(), e.getErrorCode(), e.getErrorMsg()));
        }
        object.put("status", status);
        return object;
    }
    
    
    /**
     * 向绑定到tag中的用户推送消息，即普通组播
     * @param tagName
     * @param msgExpires 相对于当前时间的消息过期时间，单位为秒
     * @param msgType 0：透传消息 1：通知 默认值为0
     * @param sendTime 待发送时间戳，用于定时消息推送. 必须在当前时间60s以外，1年(31536000s)以内，默认为null
     * @param message 消息内容
     * @param type 0: Android 1: IOS
     * @return
     */
    public static JSONObject pushToTag(String tagName, Integer msgExpires, Integer msgType, Long sendTime,
            String message, int type) {
        int status = 0;
        JSONObject object = new JSONObject();
        try {
            PushMsgToTagRequest request = new PushMsgToTagRequest()
            .addTagName(tagName)
            .addMsgExpires(msgExpires)
            .addMessageType(msgType)
//            .addSendTime(sendTime)
            .addMessage(message);
            PushMsgToTagResponse response = pushClients[type].pushMsgToTag(request);
            
            object.put("msgId", response.getMsgId());
            object.put("sendTime", response.getSendTime());
        } catch (PushClientException e) {
            status = 1;
            object.put("exception", e.toString());
        } catch (PushServerException e) {
            status = 2;
            object.put("exception", String.format(
                        "requestId: %d, errorCode: %d, errorMsg: %s",
                        e.getRequestId(), e.getErrorCode(), e.getErrorMsg()));
        }
        
        object.put("status", status);
        return object;
    }
    
    /**
     * 创建用户自定义的标签组
     * @param tagName
     * @param type 0: Android 1: IOS
     * @return
     */
    public static JSONObject createUserTag(String tagName, int type) {
        int status = 0;
        JSONObject object = new JSONObject();
        try {
            CreateTagRequest request = new CreateTagRequest().addTagName(tagName);
            CreateTagResponse response = pushClients[type].createTag(request);
            
            object.put("tagName", response.getTagName());
            object.put("result", response.getResult());
        } catch (PushClientException e) {
            status = 1;
            object.put("exception", e.toString());
        } catch (PushServerException e) {
            status = 2;
            object.put("exception", String.format(
                        "requestId: %d, errorCode: %d, errorMsg: %s",
                        e.getRequestId(), e.getErrorCode(), e.getErrorMsg()));
        }
        object.put("status", status);
        return object;     
    }
    
    /**
     * 删除用户自定义的标签组
     * @param tagName
     * @param type 0: Android 1: IOS
     * @return
     */
    public static JSONObject deleteUserTag(String tagName, int type) {
        int status = 0;
        JSONObject object = new JSONObject();
        try {
            DeleteTagRequest request = new DeleteTagRequest().addTagName(tagName);
            DeleteTagResponse response = pushClients[type].deleteTag(request);
            
            object.put("tagName", response.getTagName());
            object.put("result", response.getResult());
        } catch (PushClientException e) {
            status = 1;
            object.put("exception", e.toString());
        } catch (PushServerException e) {
            status = 2;
            object.put("exception", String.format(
                        "requestId: %d, errorCode: %d, errorMsg: %s",
                        e.getRequestId(), e.getErrorCode(), e.getErrorMsg()));
        }
        object.put("status", status);
        return object;
    }
    
    /**
     * 向标签组中批量添加设备
     * @param channelIds channelId列表数组，[channelId1, channelId2,...], 最多10个
     * @param tagName 必须是成功创建的tag，且其值不能为"default"
     * @param type 0: Android 1: IOS
     * @return
     */
    public static JSONObject addToTag(String[] channelIds, String tagName, int type) {
        int status = 0;
        JSONObject object = new JSONObject();
        try {
            AddDevicesToTagRequest request = new AddDevicesToTagRequest()
                    .addTagName(tagName).addChannelIds(channelIds);
            AddDevicesToTagResponse response = pushClients[type].addDevicesToTag(request);
            if (null != response) {
                StringBuilder strBuilder = new StringBuilder();
                strBuilder.append("devicesInTag：{");
                List<?> devicesInfo = response.getDevicesInfoAfterAdded();
                for (int i = 0; i < devicesInfo.size(); i++) {
                    Object obj = devicesInfo.get(i);
                    if (i != 0) {
                        strBuilder.append(",");
                    }
                    if (obj instanceof DeviceInfo) {
                        DeviceInfo deviceInfo = (DeviceInfo) obj;
                        strBuilder.append("{channelId:"
                                + deviceInfo.getChannelId() + ",result:"
                                + deviceInfo.getResult() + "}");
                    }
                }
                strBuilder.append("}");
                logger.trace(strBuilder);
            }
        } catch (PushClientException e) {
            status = 1;
            object.put("exception", e.toString());
        } catch (PushServerException e) {
            status = 2;
            object.put("exception", String.format(
                        "requestId: %d, errorCode: %d, errorMsg: %s",
                        e.getRequestId(), e.getErrorCode(), e.getErrorMsg()));
        }
        object.put("status", status);
        return object;
    }
    
    
    /**
     * 从标签组中批量删除设备
     * @param channelIds channelId列表. [channelId1, channelId2, ...]，最多10个
     * @param tagName 必须是成功创建的tag，且其值不能为"default"
     * @param type 0: Android 1: IOS
     * @return
     */
    public static JSONObject deleteFromTag(String[] channelIds, String tagName, int type) {
        int status = 0;
        JSONObject object = new JSONObject();
        try {
            DeleteDevicesFromTagRequest request = new DeleteDevicesFromTagRequest()
                    .addTagName(tagName).addChannelIds(channelIds);
            DeleteDevicesFromTagResponse response = pushClients[type]
                    .deleteDevicesFromTag(request);
            if (null != response) {
                StringBuilder strBuilder = new StringBuilder();
                strBuilder.append("devicesInfoAfterDel:{");
                List<?> list = response.getDevicesInfoAfterDel();
                for (int i = 0; i < list.size(); i++) {
                    if (i != 0) {
                        strBuilder.append(",");
                    }
                    Object obj = list.get(i);
                    if (obj instanceof DeviceInfo) {
                        DeviceInfo deviceInfo = (DeviceInfo) obj;
                        strBuilder.append("{channelId: "
                                + deviceInfo.getChannelId() + ", result: "
                                + deviceInfo.getResult() + "}");
                    }
                }
                strBuilder.append("}");
                logger.trace(strBuilder);
            }
        } catch (PushClientException e) {
            status = 1;
            object.put("exception", e.toString());
        } catch (PushServerException e) {
            status = 2;
            object.put("exception", String.format(
                        "requestId: %d, errorCode: %d, errorMsg: %s",
                        e.getRequestId(), e.getErrorCode(), e.getErrorMsg()));
        }
        object.put("status", status);
        return object;
    }

}
