package com.baidu.dzpk.business;


import com.baidu.dzpk.service.MongodbService;
import com.mongodb.MongoClient;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.work.comm.util.LogUtil;
import org.apache.logging.log4j.Logger;
import org.bson.Document;
import org.bson.conversions.Bson;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 更新bigpine_data_room_temp表中30天之前数据
 */
public class UpdateBigpineGameDataTempManage {
    public static Logger  logger = LogUtil.getLogger(UpdateBigpineGameDataTempManage.class);

    private static final String DB_NAME = "dzpk";
    private static final String ROOM_DATA_TEMP_COLLECTION = "bigpine_data_room_temp";

    public static void init() {
        ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);
        long oneDay = 24 * 60 * 60 * 1000;
        long initDelay  = getTimeMillis("06:30:00") - System.currentTimeMillis();
        initDelay = initDelay > 0 ? initDelay : oneDay + initDelay;

        executor.scheduleAtFixedRate(() -> {
            logger.debug("BigpineGameDataTempCheckTask current: " + System.currentTimeMillis());

            MongoClient mongo = null;
            try {
                mongo = MongodbService.getMongoInstance();
                MongoDatabase database = mongo.getDatabase(DB_NAME);
                MongoCollection<Document> roomTempCollection = database.getCollection(ROOM_DATA_TEMP_COLLECTION);

                //删除30天之前的数据
                SimpleDateFormat df = new SimpleDateFormat ("yy-MM-dd");
                long frequency = 30 * 86400 * 1000L;
                String time = df.format(new Date(System.currentTimeMillis() - frequency));

                long deleteTime = 0;
                try {
                    deleteTime = df.parse(time).getTime();
                } catch (ParseException e) {
                    e.printStackTrace();
                }

                Bson fil = new Document("time", deleteTime);
                long deleteCount = roomTempCollection.deleteMany(fil).getDeletedCount();
                logger.debug("delete day " + time + " data, deleteCount: " + deleteCount);

            } catch (Exception e) {
                logger.error("BigpineGameDataTempCheckTask Exception:" + e.getMessage(), e);
                e.printStackTrace();
            } finally {
                MongodbService.close(mongo);
            }
        }, initDelay , oneDay, TimeUnit.MILLISECONDS);
    }

    /**
     * 获取指定时间对应的毫秒数
     * @param time "HH:mm:ss"
     * @return 对应的时间戳
     */
    private static long getTimeMillis(String time) {
        try {
            DateFormat dateFormat = new SimpleDateFormat("yy-MM-dd HH:mm:ss");
            DateFormat dayFormat = new SimpleDateFormat("yy-MM-dd");
            Date curDate = dateFormat.parse(dayFormat.format(new Date()) + " " + time);
            return curDate.getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return 0;
    }
}