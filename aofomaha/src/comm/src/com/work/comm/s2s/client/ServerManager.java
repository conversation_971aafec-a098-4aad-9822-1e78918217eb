package com.work.comm.s2s.client;

import com.work.comm.s2s.common.AbstractChannelManager;
import com.work.comm.s2s.handler.ChannelManageHandler;
import com.work.comm.s2s.handler.S2BusinessHandler;
import com.work.comm.s2s.handler.S2ChannelInitializer;
import com.work.comm.s2s.processor.impl.S2ProcessorFactoryImpl;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.nio.NioEventLoopGroup;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 连接的服务器管理器
 * 功能：
 * 1. 获取和监控需连接的服务器配置
 * 2. 对业务层提供发送数据到服务器的方法
 *    * 服务类型匹配
 *    * 服务类型:服务ID匹配
 * 3. 公共配置和组件的初始化，维护和释放
 */
@Slf4j
public class ServerManager extends AbstractChannelManager {
    /** processor配置文件路径 */
    private static final String eventMappingPath = "wg.requestclassmapping.path";

    /** 单例模式 */
    private static final AtomicBoolean isInitialized = new AtomicBoolean(false);
    private static ServerManager INSTANCE = null;
    public static final ServerManager getInstance(){
        return ServerManager.INSTANCE;
    }
    /**
     * 系统启动时调用
     * 第一次调用有效
     *
     * @param properties
     */
    public static void initialize(Properties properties){
        boolean alreadyInitialize = isInitialized.getAndSet(true);
        if(alreadyInitialize)
            return;

        AppConfig appConfig = AppConfig.loadFrom(properties);
        NioEventLoopGroup workerGroup  = new NioEventLoopGroup(appConfig.getMaxWorkerThreadNum());
        INSTANCE = new ServerManager(appConfig,workerGroup);
    }
    private ServerManager(AppConfig appConfig,EventLoopGroup workerGroup){
        super(appConfig.getAppType(),appConfig.getAppId(),workerGroup,true);
        this.appConfig = appConfig;

        this.processorFactory = new S2ProcessorFactoryImpl(eventMappingPath, this);
        this.businessHandler = new S2BusinessHandler(this.processorFactory);
        this.channelInitializer = new S2ChannelInitializer(1L,this.appConfig,
                new ChannelManageHandler(this),
                this.businessHandler);

        // 连接指定的服务器
        for(ServerConfig config : this.appConfig.getServerConfigLst())
            this.addServer(config);
    }

    // 所在应用标识
    private AppConfig appConfig;
    public AppConfig getAppConfig(){
        return this.appConfig;
    }

    // 支持的数据处理器
    private S2ProcessorFactoryImpl processorFactory;

    // Netty承接到业务层的Handler
    private S2BusinessHandler businessHandler;

    // Channel初始器
    private S2ChannelInitializer channelInitializer;

    /**
     * connector集合,连接的每个服务器对应唯一个
     * ip:port 值类型：Connector
     *
     * 用于防止创建多个到同一个服务器的Connector
     */
    private Map<String,Connector> connectorMap = new ConcurrentHashMap<>();
    public void addServer(String accessIp,int accessPort) {
        if(null == accessIp || "".equals(accessIp.trim()))
            return;
        if(accessPort<=0)
            return;

        ServerConfig config = new ServerConfig();
        config.setAccessIp(accessIp.trim());
        config.setAccessPort(accessPort);
        AppConfig.mergeConfig(this.appConfig,config);
        this.addServer(config);
    }
    public void delServer(String accessIp,int accessPort) {
        if(null == accessIp || "".equals(accessIp.trim()))
            return;
        if(accessPort<=0)
            return;

        synchronized (this.connectorMap) {
            String key = this.ipPortKey(accessIp.trim(), accessPort);
            Connector connector = this.connectorMap.get(key);
            if (null == connector) {
                log.warn("服务器[{}:{}]的连接器不存在！！",accessIp,accessPort);
            }

            this.connectorMap.remove(key);
            connector.destroy();
        }
    }
    private void addServer(ServerConfig config) {
        if(null == config)
            return;
        if(null == config.getAccessIp() || "".equals(config.getAccessIp().trim()))
            return;
        if(config.getAccessPort()<=0)
            return;

        synchronized (this.connectorMap) {
            String key = this.ipPortKey(config.getAccessIp().trim(), config.getAccessPort());
            Connector connector = this.connectorMap.get(key);
            if (null == connector) {
                connector = new Connector(config.getAccessIp(),config.getAccessPort(),
                        config.getPoolCount(),config.getPoolCheckIntervalSec(),
                        this.workerGroup,this.channelInitializer);
                this.connectorMap.put(key,connector);
                log.info("成功添加服务器[{}:{}]的链接器，系统将会自动连接/重连服务器！！",config.getAccessIp(),config.getAccessPort());
                return;
            }

            log.info("服务器[{}:{}]的连接器已经存在，系统将会自动连接/重连服务器！！",config.getAccessIp(),config.getAccessPort());
        }
    }

    /** 实现父类方法 */
    protected int getMaxConnNumPerApp(){
        return this.appConfig.getPoolCount();
    }
    protected int getReadIdleMaxNum(){
        return this.appConfig.getReadIdleMaxNum();
    }
    protected int getAuthTimeoutSec(){
        return this.appConfig.getAuthTimeoutSec();
    }

    /**
     * 系统关闭时调用，作用：
     * 1.关闭已经打开的连接
     * 2.关闭IO线程池
     *
     * 调用父类的destroy方法前，
     * 先将connector的重连功能关闭
     */
    @Override
    public void destroy(){
        this.isDestroy = true;
        Iterator<String> it = this.connectorMap.keySet().iterator();
        while(it.hasNext()){
            String key = it.next();
            Connector connector = this.connectorMap.remove(key);
            connector.destroy();
        }

        super.destroy();
    }
}
