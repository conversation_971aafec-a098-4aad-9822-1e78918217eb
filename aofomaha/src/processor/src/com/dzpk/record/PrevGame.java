package com.dzpk.record;

import java.util.HashMap;
import java.util.Map;

public class PrevGame {
    private Map<Integer, PrevPlayer> prevPlayers = new HashMap<Integer, PrevPlayer>();   // 牌桌玩家
    private Integer[] deskPocers = new Integer[5];  // 底牌
    private boolean compare;                        // 是否有进行比牌产生结果
    private int bigBlind;       // 大盲位置
    private int smallBlind;     // 小盲位置
    private int bigBlindChip;   // 大盲筹码
    private int smallBlindChip; // 小盲筹码
    private int qianzhu;        // 前注
    private int insurance;      // 保险收支
    private int fund;           // 基金(抽水)
    private int dealer;         // 庄家位置
    private int seats;          // 位置数
    private Integer[] winners;  // 赢牌玩家列表
    private int muckSwitchStatus; // muck开关状态
    private int currentBetJackPot = 0;//当前手的jackpot投入量
    
    public PrevGame() {
        for (int i = 0; i < deskPocers.length; i++) {
            deskPocers[i] = -1;
        }
    }
    
    public Integer[] getDeskPocers() {
        return deskPocers;
    }
    
    public Map<Integer, PrevPlayer> getPrevPlayers() {
        return prevPlayers;
    }
    
    public boolean getCompare() {
        return compare;
    }
    
    public void setCompare(boolean compare) {
        this.compare = compare;
    }

    public int getBigBlind() {
        return bigBlind;
    }

    public void setBigBlind(int bigBlind) {
        this.bigBlind = bigBlind;
    }

    public int getSmallBlind() {
        return smallBlind;
    }

    public void setSmallBlind(int smallBlind) {
        this.smallBlind = smallBlind;
    }

    public int getBigBlindChip() {
        return bigBlindChip;
    }

    public void setBigBlindChip(int bigBlindChip) {
        this.bigBlindChip = bigBlindChip;
    }

    public int getSmallBlindChip() {
        return smallBlindChip;
    }

    public void setSmallBlindChip(int smallBlindChip) {
        this.smallBlindChip = smallBlindChip;
    }

    public int getDealer() {
        return dealer;
    }

    public void setDealer(int dealer) {
        this.dealer = dealer;
    }

    public int getSeats() {
        return seats;
    }

    public void setSeats(int seats) {
        this.seats = seats;
    }
    
    public Integer[] getWinners() {
        return winners;
    }
    
    public void setWinners(Integer[] winners) {
        this.winners = winners;
    }

    public int getQianzhu() {
        return qianzhu;
    }

    public void setQianzhu(int qianzhu) {
        this.qianzhu = qianzhu;
    }

    public int getInsurance() {
        return insurance;
    }

    public void setInsurance(int insurance) {
        this.insurance = insurance;
    }

    public int getFund() {
        return fund;
    }

    public void setFund(int fund) {
        this.fund = fund;
    }
    
    /**
     * @return the muckSwitchStatus
     */
    public int getMuckSwitchStatus() {
        return muckSwitchStatus;
    }

    /**
     * @param muckSwitchStatus the muckSwitchStatus to set
     */
    public void setMuckSwitchStatus(int muckSwitchStatus) {
        this.muckSwitchStatus = muckSwitchStatus;
    }

    public int getCurrentBetJackPot() {
        return currentBetJackPot;
    }

    public void setCurrentBetJackPot(int currentBetJackPot) {
        this.currentBetJackPot = currentBetJackPot;
    }
}
